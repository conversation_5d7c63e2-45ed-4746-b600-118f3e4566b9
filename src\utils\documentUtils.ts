import { Document } from '@/types'
import { processDocument, deleteStoredEmbeddings } from '@/services/embeddingService'

/**
 * Document storage and management utilities
 */

/**
 * Process document embeddings with timeout and error handling
 */
async function processDocumentEmbeddings(document: Document): Promise<void> {
  try {
    // Add timeout to prevent hanging
    const embeddingPromise = processDocument(document)
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Embedding generation timeout')), 30000) // 30 second timeout
    })

    await Promise.race([embeddingPromise, timeoutPromise])
    console.log(`Successfully generated embeddings for document: ${document.name}`)
  } catch (error) {
    console.warn('Failed to generate embeddings for document:', error)
    // Document processing should still succeed even if embeddings fail
    // This ensures the process doesn't hang or fail completely
  }
}

export function getDocuments(): Document[] {
  try {
    const data = localStorage.getItem('chatdoc_documents')
    return data ? JSON.parse(data) : []
  } catch {
    return []
  }
}

export function saveDocument(document: Document): void {
  try {
    const documents = getDocuments()
    const updated = documents.filter(d => d.id !== document.id)
    updated.push(document)
    localStorage.setItem('chatdoc_documents', JSON.stringify(updated))
  } catch (error) {
    console.error('Failed to save document:', error)
  }
}

export async function uploadDocument(file: File): Promise<Document> {
  const { validateFile, readFileContent, generateId } = await import('./fileUtils')
  
  const validation = validateFile(file)
  if (!validation.isValid) {
    throw new Error(validation.error || 'Invalid file')
  }

  const content = await readFileContent(file)
  
  // Map file extension to document type
  let docType: 'txt' | 'md' | 'web' = 'txt'
  if (file.name.toLowerCase().endsWith('.md')) {
    docType = 'md'
  } else if (file.name.toLowerCase().endsWith('.pdf')) {
    docType = 'txt' // Treat PDF as text for now
  }
  
  const document: Document = {
    id: generateId(),
    name: file.name,
    type: docType,
    size: file.size,
    content,
    uploadedAt: new Date()
  }

  // Save document first to ensure it's available even if embedding fails
  saveDocument(document)

  // Generate embeddings for the document
  await processDocumentEmbeddings(document)

  return document
}

/**
 * Save a document with embedding processing (for web-extracted content)
 * This ensures web documents go through the same processing pipeline as uploaded files
 */
export async function saveDocumentWithEmbeddings(document: Document): Promise<Document> {
  console.log(`Processing web document for embeddings: ${document.name}`)

  // Save document first to ensure it's available even if embedding fails
  saveDocument(document)

  // Generate embeddings for the document
  await processDocumentEmbeddings(document)

  return document
}

export function deleteDocument(id: string): void {
  try {
    const documents = getDocuments()
    const updated = documents.filter(d => d.id !== id)
    localStorage.setItem('chatdoc_documents', JSON.stringify(updated))
  } catch (error) {
    console.error('Failed to delete document:', error)
  }
  
  // Clean up embeddings for the deleted document
  try {
    deleteStoredEmbeddings(id)
  } catch (error) {
    console.error('Failed to delete embeddings for document:', error)
  }
}

export function renameDocument(id: string, newName: string): void {
  // Validate the new name
  const trimmedName = newName.trim()
  if (!trimmedName) {
    throw new Error('Document name cannot be empty')
  }
  if (trimmedName.length > 255) {
    throw new Error('Document name is too long (max 255 characters)')
  }

  try {
    const documents = getDocuments()
    const documentIndex = documents.findIndex(d => d.id === id)
    
    if (documentIndex === -1) {
      throw new Error('Document not found')
    }

    // Update the document name
    documents[documentIndex] = {
      ...documents[documentIndex],
      name: trimmedName
    }

    localStorage.setItem('chatdoc_documents', JSON.stringify(documents))
  } catch (error) {
    console.error('Failed to rename document:', error)
    throw error
  }
}