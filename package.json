{"name": "chatdoc-v3", "version": "0.1.0", "private": true, "scripts": {"dev": "concurrently \"npm run dev:vite\" \"npm run dev:proxy\"", "dev:vite": "vite", "dev:proxy": "node proxy-server.js", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"@mozilla/readability": "^0.6.0", "@types/react-syntax-highlighter": "^15.5.13", "clsx": "^2.0.0", "concurrently": "^9.2.0", "cors": "^2.8.5", "date-fns": "^4.1.0", "dom-to-semantic-markdown": "^1.5.0", "express": "^5.1.0", "http-proxy-middleware": "^3.0.5", "jsdom": "^26.1.0", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-error-boundary": "^6.0.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.3", "remark-gfm": "^4.0.1", "sonner": "^2.0.7"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.1.1", "@vitest/ui": "^0.34.6", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^5.0.0", "vitest": "^0.34.6"}}