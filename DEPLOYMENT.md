# Deployment Guide

This guide covers deployment options for ChatDoc v3, including local development, production builds, and cloud deployment.

## Development Deployment

### Local Development Setup

1. **Clone and Install**:
```bash
git clone <repository-url>
cd chatdoc-v3
npm install
```

2. **Environment Configuration**:
```bash
# Create .env file
cp .env.example .env

# Configure API endpoints
VITE_REASONING_API_URL=your_llm_endpoint
VITE_REASONING_API_KEY=your_api_key
VITE_REASONING_MODEL=your_model_name
VITE_EMBEDDING_API_URL=your_embedding_endpoint
VITE_EMBEDDING_API_KEY=your_embedding_key
VITE_EMBEDDING_MODEL=your_embedding_model
```

3. **Start Development Servers**:
```bash
# Start both Vite and proxy server
npm run dev

# Or start separately
npm run dev:vite    # Vite on port 3000
npm run dev:proxy   # Proxy on port 9527
```

### Development Architecture

```
Browser (localhost:3000)
    ↓
Vite Dev Server
    ↓ (API proxy)
Local Proxy Server (localhost:9527)
    ↓
External APIs (LLM, Embedding, Web Content)
```

## Production Deployment

### Build Process

1. **Environment Setup**:
```bash
# Set production environment variables
export VITE_REASONING_API_URL=production_llm_endpoint
export VITE_REASONING_API_KEY=production_api_key
# ... other variables
```

2. **Build Application**:
```bash
# TypeScript compilation + Vite build
npm run build

# Output in dist/ directory
ls dist/
# ├── assets/          # Bundled JS/CSS
# ├── index.html       # Entry point
# └── vite.svg         # Static assets
```

3. **Preview Build Locally**:
```bash
npm run preview
# Opens on http://localhost:4173
```

### Static Hosting Options

#### Option 1: Vercel (Recommended)

1. **Vercel Configuration** (`vercel.json`):
```json
{
  "buildCommand": "npm run build",
  "outputDirectory": "dist",
  "framework": "vite",
  "env": {
    "VITE_REASONING_API_URL": "@reasoning-api-url",
    "VITE_REASONING_API_KEY": "@reasoning-api-key",
    "VITE_REASONING_MODEL": "@reasoning-model",
    "VITE_EMBEDDING_API_URL": "@embedding-api-url", 
    "VITE_EMBEDDING_API_KEY": "@embedding-api-key",
    "VITE_EMBEDDING_MODEL": "@embedding-model"
  }
}
```

2. **Deploy Commands**:
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel

# Set environment variables in Vercel dashboard
# or via CLI:
vercel env add VITE_REASONING_API_URL
```

#### Option 2: Netlify

1. **Build Settings**:
   - Build Command: `npm run build`
   - Publish Directory: `dist`
   - Node Version: 18+

2. **Environment Variables**:
   Set in Netlify dashboard under Site Settings > Environment Variables

3. **Redirects** (`public/_redirects`):
```
/*    /index.html   200
```

#### Option 3: GitHub Pages

1. **GitHub Actions** (`.github/workflows/deploy.yml`):
```yaml
name: Deploy to GitHub Pages
on:
  push:
    branches: [ main ]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
      - run: npm ci
      - run: npm run build
        env:
          VITE_REASONING_API_URL: ${{ secrets.REASONING_API_URL }}
          VITE_REASONING_API_KEY: ${{ secrets.REASONING_API_KEY }}
          # ... other environment variables
      - uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./dist
```

### Server-Side Deployment

For proxy server functionality in production:

#### Option 1: Node.js Server

1. **Server Setup**:
```javascript
// server.js
const express = require('express')
const path = require('path')
const proxyMiddleware = require('./proxy-server.js')

const app = express()
const PORT = process.env.PORT || 3000

// Serve static files
app.use(express.static(path.join(__dirname, 'dist')))

// Add proxy middleware
app.use('/api/web-proxy', proxyMiddleware)

// Handle SPA routing
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist/index.html'))
})

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`)
})
```

2. **Deploy to Platform**:
```bash
# Heroku
heroku create your-app-name
git push heroku main

# Railway
railway login
railway new
railway up

# DigitalOcean App Platform
doctl apps create --spec app.yaml
```

#### Option 2: Docker Deployment

1. **Dockerfile**:
```dockerfile
# Build stage
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

# Production stage
FROM node:18-alpine
WORKDIR /app
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/proxy-server.js ./
COPY --from=builder /app/package*.json ./
RUN npm ci --only=production

EXPOSE 3000
CMD ["node", "server.js"]
```

2. **Docker Compose** (`docker-compose.yml`):
```yaml
version: '3.8'
services:
  chatdoc:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - VITE_REASONING_API_URL=${REASONING_API_URL}
      - VITE_REASONING_API_KEY=${REASONING_API_KEY}
      # ... other env vars
    restart: unless-stopped
```

## Environment Configuration

### Environment Variables by Deployment Type

#### Static Hosting (Client-Side Only)
```env
# Required for all static deployments
VITE_REASONING_API_URL=https://api.openai.com/v1/chat/completions
VITE_REASONING_API_KEY=your_openai_key
VITE_REASONING_MODEL=gpt-3.5-turbo
VITE_EMBEDDING_API_URL=https://api.openai.com/v1/embeddings
VITE_EMBEDDING_API_KEY=your_openai_key  
VITE_EMBEDDING_MODEL=text-embedding-ada-002
```

#### Server-Side Deployment
```env
# Add these for server-side proxy functionality
NODE_ENV=production
PORT=3000
PROXY_PORT=9527

# Client-side variables (same as above)
VITE_REASONING_API_URL=...
# ... rest of VITE_ variables
```

### API Provider Configuration

#### DeepSeek Configuration
```env
VITE_REASONING_API_URL=https://api.deepseek.com/v1/chat/completions
VITE_REASONING_API_KEY=your_deepseek_key
VITE_REASONING_MODEL=deepseek-chat
```

#### OpenAI Configuration
```env
VITE_REASONING_API_URL=https://api.openai.com/v1/chat/completions
VITE_REASONING_API_KEY=your_openai_key
VITE_REASONING_MODEL=gpt-3.5-turbo
VITE_EMBEDDING_API_URL=https://api.openai.com/v1/embeddings
VITE_EMBEDDING_MODEL=text-embedding-ada-002
```

#### Doubao (ByteDance) Configuration
```env
VITE_EMBEDDING_API_URL=https://ark.cn-beijing.volces.com/api/v3/embeddings
VITE_EMBEDDING_API_KEY=your_doubao_key
VITE_EMBEDDING_MODEL=doubao-embedding-v1
```

## Performance Optimization

### Build Optimization

1. **Vite Configuration** (`vite.config.ts`):
```typescript
export default defineConfig({
  build: {
    target: 'es2015',
    outDir: 'dist',
    sourcemap: false,
    minify: 'esbuild',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          markdown: ['react-markdown', 'remark-gfm']
        }
      }
    }
  }
})
```

2. **Bundle Analysis**:
```bash
npm run build -- --analyze
# or
npx vite-bundle-analyzer dist
```

### Runtime Optimization

1. **Service Worker** (optional, `public/sw.js`):
```javascript
// Cache static assets
const CACHE_NAME = 'chatdoc-v3-cache-v1'
const urlsToCache = [
  '/',
  '/static/js/bundle.js',
  '/static/css/main.css'
]

self.addEventListener('install', event => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => cache.addAll(urlsToCache))
  )
})
```

2. **CDN Configuration**:
```javascript
// Use CDN for external libraries
const CDN_LIBRARIES = {
  'react': 'https://unpkg.com/react@18/umd/react.production.min.js',
  'react-dom': 'https://unpkg.com/react-dom@18/umd/react-dom.production.min.js'
}
```

## Monitoring & Analytics

### Error Tracking

1. **Sentry Integration**:
```javascript
import * as Sentry from '@sentry/react'

Sentry.init({
  dsn: process.env.VITE_SENTRY_DSN,
  environment: process.env.NODE_ENV
})
```

2. **Custom Error Boundary**:
```typescript
class ProductionErrorBoundary extends Component {
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    Sentry.captureException(error, { extra: errorInfo })
  }
}
```

### Analytics

1. **Google Analytics**:
```javascript
// Add to index.html
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_TRACKING_ID"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_TRACKING_ID');
</script>
```

2. **Usage Tracking**:
```typescript
// Track user interactions
const trackEvent = (action: string, category: string) => {
  gtag('event', action, {
    event_category: category,
    event_label: window.location.pathname
  })
}
```

## Security Considerations

### Production Security

1. **Environment Variables**:
   - Never commit API keys to version control
   - Use secure secret management (Vercel/Netlify secrets)
   - Rotate API keys regularly

2. **Content Security Policy**:
```html
<meta http-equiv="Content-Security-Policy" 
      content="default-src 'self'; 
               script-src 'self' 'unsafe-inline';
               style-src 'self' 'unsafe-inline';
               connect-src 'self' https://api.openai.com https://api.deepseek.com;">
```

3. **HTTPS Enforcement**:
```javascript
// Redirect to HTTPS in production
if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
  location.replace('https:' + window.location.href.substring(window.location.protocol.length))
}
```

### API Security

1. **Rate Limiting**:
   - Implement client-side rate limiting
   - Use API provider rate limiting features
   - Monitor API usage and costs

2. **CORS Configuration**:
   - Configure proper CORS headers
   - Whitelist specific domains in production
   - Use proxy server for sensitive operations

## Troubleshooting

### Common Deployment Issues

1. **Build Failures**:
```bash
# Clear cache and rebuild
rm -rf node_modules/.cache
rm -rf dist
npm ci
npm run build
```

2. **Environment Variable Issues**:
```bash
# Check build-time variables
npm run build -- --debug
```

3. **CORS Issues in Production**:
   - Ensure proxy server is deployed alongside static files
   - Configure API providers for your domain
   - Check browser network tab for CORS errors

4. **Performance Issues**:
   - Analyze bundle size: `npm run build -- --analyze`
   - Check lighthouse scores
   - Monitor API response times

### Health Checks

1. **Application Health**:
```javascript
// Add to your app
export const healthCheck = async () => {
  try {
    // Test API connectivity
    await fetch('/api/health')
    return { status: 'healthy' }
  } catch (error) {
    return { status: 'unhealthy', error: error.message }
  }
}
```

2. **Monitoring Endpoints**:
```javascript
// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version
  })
})
```