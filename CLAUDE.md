# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

ChatDoc v3 is a React-based AI document chat assistant that implements client-side RAG (Retrieval-Augmented Generation). Users can upload documents (TXT, MD) or extract web content, then chat with the content using AI. The application features vector embeddings for document search, persistent chat sessions, and a modern TypeScript/React interface.

## Current Implementation Status

✅ **Fully Implemented Features:**
- Document upload and processing (TXT, MD files)
- Web content extraction with proxy server
- Client-side vector embeddings and similarity search
- RAG-powered chat with source attribution
- Session management with persistence
- Document management (upload, rename, delete)
- Responsive UI with modern design
- TypeScript strict configuration
- Service-oriented architecture

⚠️ **Not Yet Implemented:**
- PDF/DOCX file support
- User authentication
- Server-side persistence
- Export functionality

## Development Commands

### Essential Commands
- `npm run dev` - Start development server with proxy (opens at http://localhost:3000)
- `npm run dev:vite` - Start Vite development server only
- `npm run dev:proxy` - Start proxy server only (port 9527)
- `npm run build` - Build for production (TypeScript compilation + Vite build)
- `npm run lint` - Run ESLint with TypeScript support
- `npm run test` - Run Vitest test suite
- `npm run test:ui` - Run tests with UI interface
- `npm run preview` - Preview production build

### Environment Setup
Copy `.env.example` to `.env` and configure:
- `VITE_REASONING_API_URL` - LLM API endpoint (default: DeepSeek)
- `VITE_REASONING_API_KEY` - LLM API key
- `VITE_REASONING_MODEL` - Model name (default: deepseek-chat)
- `VITE_EMBEDDING_API_URL` - Embedding API endpoint (default: Doubao)
- `VITE_EMBEDDING_API_KEY` - Embedding API key
- `VITE_EMBEDDING_MODEL` - Embedding model (default: doubao-embedding-v1)

## Architecture

### Core Architecture Pattern
- **Client-side RAG**: Document processing, embedding generation, and similarity search happen entirely in the browser
- **Service Layer**: Modular services for chat logic, document management, and embedding operations
- **State Management**: React hooks + localStorage for persistence
- **Type Safety**: Full TypeScript with strict configuration

### Key Architectural Components

#### 1. Document Processing Pipeline
```
File Upload → Text Extraction → Chunking → Embedding Generation → Vector Storage (localStorage)
```

#### 2. Chat RAG Flow
```
User Query → Embedding Generation → Vector Similarity Search → Context Assembly → LLM Request → Response
```

#### 3. Service Layer Architecture
- **ChatService** (`src/services/chatService.ts`): RAG logic, LLM integration, chat session management
- **EmbeddingService** (`src/services/embeddingService.ts`): Vector embeddings, similarity search, chunking
- **DocumentApiService** (`src/services/documentApiService.ts`): Document CRUD operations with localStorage
- **WebExtractionService** (`src/services/webExtractionService.ts`): Web content extraction with retry logic

### Key Design Patterns

#### Path Aliases
- Use `@/` for all src imports (configured in vite.config.ts and tsconfig.json)
- Example: `import { ChatService } from '@/services/chatService'`

#### API Configuration
- Centralized API configuration using environment variables
- Supports multiple LLM providers (DeepSeek, OpenAI, etc.)
- Environment-based configuration with proxy server for CORS
- Dynamic proxy routing via Vite configuration

#### Storage Strategy
- Documents and embeddings stored in localStorage as JSON
- Chat sessions persisted with full conversation history
- Automatic data serialization/deserialization with type safety

### Component Structure

#### Core Components
- **MainChatInterface**: Main application container with state management and layout
- **ChatSidebar**: Session management, document list, and navigation  
- **ChatInput**: Enhanced message input with document selection
- **MessageBubble**: Chat message display with source attribution and reasoning
- **DocumentManagerModal**: Comprehensive document management interface
- **UrlInput**: Web content extraction with authentication support

#### Utility Components
- **DocumentSelector**: Multi-document selection interface
- **LoadingIndicator**: Consistent loading states
- **DocumentRenameInput**: Inline document renaming
- **SessionRenameInput**: Inline session renaming

### State Management Patterns

#### State Architecture
- **MainChatInterface**: Central state management for the entire application
- **Local Component State**: UI-specific state (loading, editing, etc.)
- **Persistent Storage**: localStorage for documents, sessions, and embeddings
- **Service Integration**: State updates through service layer calls

#### Data Flow
```
User Action → Component State → Service Layer → Storage/API → State Update → UI Refresh
```

## Important Implementation Notes

### Vector Embeddings
- Client-side embedding generation using configurable APIs
- Cosine similarity search with configurable threshold (0.3 default)
- Document chunking with overlap for better context retrieval
- Embeddings stored with document chunks for efficient lookup

### Chat System
- Session-based conversations with document context
- Automatic title generation from first user message
- Source attribution with similarity scores
- Configurable context window (5 chunks max, 6 message history)

### Type System
Key types in `src/types/index.ts`:
- **Document**: File metadata and content
- **ChatMessage**: Message with role, content, sources, and reasoning
- **DocumentChunk**: Text chunks with embeddings and positional data
- **ChatSession**: Conversation container with document associations

### Error Handling
- Global ErrorBoundary for React errors
- Service-level error handling with user-friendly messages
- API configuration validation before requests
- Graceful degradation for missing API credentials

## Testing Strategy

- **Vitest** for unit testing
- Test configuration in `vitest.config.ts`
- Focus on service layer logic and utility functions
- UI testing through component interaction tests

## Build Configuration

- **Vite** build tool with React plugin
- **TypeScript** with strict mode enabled
- **Tailwind CSS** for styling with custom color palette
- **ESLint** with React and TypeScript rules
- Source maps enabled for debugging
- Path mapping for clean imports

## Styling Approach

- **Tailwind CSS** utility-first approach
- Custom color palette: `primary.100` (light blue), `primary.600` (blue)
- Inter font family system
- Component-scoped styling patterns
- Responsive design with mobile-first approach

## API Integration

### LLM Integration
- Configurable provider support (DeepSeek, OpenAI, etc.)
- Standardized request/response format
- Error handling with fallback messages
- Context assembly with document sources and chat history

### Future Server Integration
- DocumentApiService prepared for server-side document management
- RESTful API patterns for CRUD operations
- File upload handling prepared for server endpoints
- Document sharing and permissions (planned)
- Authentication integration points ready

## Development Guidelines

### Code Organization
- Feature-based organization in services and components
- Shared utilities in utils directory
- Type definitions centralized in types directory
- Custom hooks for reusable stateful logic

### Import Patterns
- Use path aliases (@/) for all internal imports
- Group imports: React/external libraries → internal components → types → utils
- Prefer named exports over default exports except for pages and main components

### Error Boundaries
- All async operations wrapped in try-catch blocks
- User-facing error messages for API failures
- Console logging for debugging information
- Graceful fallbacks for non-critical features