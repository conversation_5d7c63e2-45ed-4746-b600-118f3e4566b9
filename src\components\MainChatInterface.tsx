import { useRef, useEffect, useState } from 'react'
import {
  <PERSON><PERSON><PERSON>,
  MessageSquare,
  ChevronLeft,
  Edit2,
  <PERSON>,
  <PERSON>,
  <PERSON>Up,
  MessageCircle
} from 'lucide-react'

import { toast } from 'sonner'
import { getAllSessions, createSession, addMessageToSession, getSession, processMessage, deleteSession, toggleSessionFavorite, updateSessionDocuments, renameSession as renameSessionService } from '@/services/chatService'
import { ChatMessage, ChatSession, Document } from '@/types'
import { getDocuments } from '@/utils/documentUtils'
import { DocumentSelector } from './DocumentSelector'
import ChatSidebar from './ChatSidebar'
import DocumentManagerModal from './DocumentManagerModal'
import { MessageBubble } from './MessageBubble'
import SessionRenameInput from './SessionRenameInput'



export default function MainChatInterface() {
  // Chat state
  const [sessions, setSessions] = useState<ChatSession[]>([])
  const [currentSession, setCurrentSession] = useState<ChatSession | null>(null)
  const [documents, setDocuments] = useState<Document[]>([])
  const [selectedDocumentIds, setSelectedDocumentIds] = useState<string[]>([])
  const [isLoading, setLoading] = useState(false)
  const [isProcessing, setProcessing] = useState(false)

  // UI State
  const [sidebarExpanded, setSidebarExpanded] = useState(true)
  const [isDarkMode, setIsDarkMode] = useState(false)
  const [isRenamingSession, setIsRenamingSession] = useState(false)
  const [inputValue, setInputValue] = useState('')
  const [attachmentModalOpen, setAttachmentModalOpen] = useState(false)

  // UI State helper functions
  const toggleSidebar = () => setSidebarExpanded(!sidebarExpanded)
  const toggleDarkMode = () => setIsDarkMode(!isDarkMode)
  const clearInput = () => setInputValue('')

  // Session renaming functionality
  const handleRenameSession = (newTitle: string): boolean => {
    if (!currentSession) return false

    const success = renameSessionService(currentSession.id, newTitle)
    if (success) {
      const updatedSession = { ...currentSession, title: newTitle, updatedAt: new Date() }
      setSessions(sessions.map(s => s.id === updatedSession.id ? updatedSession : s))
      setCurrentSession(updatedSession)
      setIsRenamingSession(false)
      toast.success('Session renamed successfully')
      return true
    }
    toast.error('Failed to rename session')
    return false
  }

  const handleRenameSessionById = (sessionId: string, newTitle: string) => {
    const success = renameSessionService(sessionId, newTitle)
    if (success) {
      const session = sessions.find(s => s.id === sessionId)
      if (session) {
        const updatedSession = { ...session, title: newTitle, updatedAt: new Date() }
        setSessions(sessions.map(s => s.id === sessionId ? updatedSession : s))
        if (currentSession?.id === sessionId) {
          setCurrentSession(updatedSession)
        }
        toast.success('Session renamed successfully')
      }
    } else {
      toast.error('Failed to rename session')
    }
  }
  
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [currentSession?.messages])

  // Handle responsive sidebar behavior
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 768) {
        // Desktop: ensure sidebar is expanded by default
        if (!sidebarExpanded) {
          setSidebarExpanded(true)
        }
      } else {
        // Mobile: collapse sidebar by default
        setSidebarExpanded(false)
      }
    }

    // Set initial state
    handleResize()
    
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && window.innerWidth < 768) {
        setSidebarExpanded(false)
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [])

  // Load initial data
  useEffect(() => {
    const loadInitialData = async () => {
      setLoading(true)
      try {
        const [loadedSessions, loadedDocuments] = await Promise.all([
          getAllSessions(),
          getDocuments()
        ])
        setSessions(loadedSessions)
        setDocuments(loadedDocuments)
      } catch (error) {
        console.error('Failed to load initial data:', error)
        toast.error('Failed to load data')
      } finally {
        setLoading(false)
      }
    }

    loadInitialData()
  }, [])

  // Auto-resize textarea
  const autoResizeTextarea = (textarea: HTMLTextAreaElement) => {
    textarea.style.height = 'auto'
    textarea.style.height = `${Math.min(textarea.scrollHeight, 96)}px`
  }



  const handleSendMessage = async () => {
    if (!inputValue.trim()) return

    const messageContent = inputValue.trim()
    clearInput()

    try {
      let session = currentSession
      
      // Create new session if none exists
      if (!session) {
        session = createSession(selectedDocumentIds)
        setCurrentSession(session)
        setSessions([session, ...sessions])
      }

      // Add user message
      const userMessage: ChatMessage = {
        id: Date.now().toString(),
        role: 'user',
        content: messageContent,
        timestamp: new Date()
      }

      addMessageToSession(session.id, userMessage)
      // Get the updated session after adding the user message
      const sessionAfterUserMessage = getSession(session.id)
      session = sessionAfterUserMessage!
      setCurrentSession(session)
      setSessions(sessions.map(s => s.id === session!.id ? session! : s))

      setProcessing(true)

      // Process message and get AI response
      const aiMessage = await processMessage(
        session.id,
        userMessage.content,
        selectedDocumentIds
      )

      addMessageToSession(session.id, aiMessage)
      // Get the updated session after adding the message
      const updatedSession = getSession(session.id)
      session = updatedSession!
      setCurrentSession(session)
      setSessions(sessions.map(s => s.id === session!.id ? session! : s))

    } catch (error) {
      console.error('Failed to send message:', error)
      toast.error('Failed to send message')
    } finally {
      setProcessing(false)
    }
  }

  const handleNewChat = () => {
    // Create a new chat session
    const newSession = createSession(selectedDocumentIds)
    setCurrentSession(newSession)
    setSessions([newSession, ...sessions])
    
    // Reset UI state
    setSelectedDocumentIds([])
  }



  const handleSessionSelect = (session: ChatSession) => {
    setCurrentSession(session)
    setSelectedDocumentIds(session.documentIds || [])
    // Only collapse sidebar on mobile devices (< 768px)
    if (window.innerWidth < 768) {
      setSidebarExpanded(false)
    }
  }

  const handleSessionDelete = (sessionId: string) => {
    deleteSession(sessionId)
    const updatedSessions = sessions.filter(s => s.id !== sessionId)
    setSessions(updatedSessions)

    if (currentSession?.id === sessionId) {
      setCurrentSession(null)
      setSelectedDocumentIds([])
    }

    toast.success('Session deleted')
  }

  const handleToggleFavorite = (sessionId: string) => {
    toggleSessionFavorite(sessionId)
    const updatedSession = getSession(sessionId)
    if (updatedSession) {
      setSessions(sessions.map(s => s.id === sessionId ? updatedSession : s))
      if (currentSession?.id === sessionId) {
        setCurrentSession(updatedSession)
      }
    }
  }

  const handleDocumentSelectionChange = (documentIds: string[]) => {
    setSelectedDocumentIds(documentIds)

    // Persist changes to current session if one exists
    if (currentSession) {
      updateSessionDocuments(currentSession.id, documentIds)
      // Update the current session state to reflect the changes
      const updatedSession = { ...currentSession, documentIds, updatedAt: new Date() }
      setCurrentSession(updatedSession)
      // Also update the session in the sessions list
      setSessions(sessions.map(s => s.id === currentSession.id ? updatedSession : s))
    }
  }

  const handleMessageAction = (action: string, message: ChatMessage) => {
    switch (action) {
      case 'copy':
        navigator.clipboard.writeText(message.content)
        toast.success('Message copied to clipboard')
        break
      case 'refresh':
        // Implement refresh logic if needed
        toast.info('Refresh functionality not implemented yet')
        break
      case 'thumbsUp':
      case 'thumbsDown':
        // Implement feedback logic if needed
        toast.success('Feedback recorded')
        break
      default:
        break
    }
  }



  const handleDocumentsUpdate = async () => {
    try {
      // Reload documents from localStorage
      const updatedDocuments = await getDocuments()
      setDocuments(updatedDocuments)

      // Clean up selectedDocumentIds - remove any IDs that no longer exist
      const validDocumentIds = updatedDocuments.map(doc => doc.id)
      const cleanedSelectedIds = selectedDocumentIds.filter(id => validDocumentIds.includes(id))

      // Update selectedDocumentIds if any were removed
      if (cleanedSelectedIds.length !== selectedDocumentIds.length) {
        setSelectedDocumentIds(cleanedSelectedIds)

        // Update current session's document IDs if it exists
        if (currentSession) {
          updateSessionDocuments(currentSession.id, cleanedSelectedIds)
          const updatedSession = { ...currentSession, documentIds: cleanedSelectedIds, updatedAt: new Date() }
          setCurrentSession(updatedSession)
          setSessions(sessions.map(s => s.id === currentSession.id ? updatedSession : s))
        }
      }
    } catch (error) {
      console.error('Failed to reload documents:', error)
      toast.error('Failed to refresh documents')
    }
  }

  return (
    <div className="flex h-screen bg-gray-50">
      
      {/* Sidebar */}
      <ChatSidebar
        isExpanded={sidebarExpanded}
        onToggleExpanded={() => setSidebarExpanded(!sidebarExpanded)}
        sessions={sessions}
        currentSession={currentSession}
        documents={documents}
        selectedDocumentIds={selectedDocumentIds}
        onSessionSelect={handleSessionSelect}
        onDeleteSession={handleSessionDelete}
        onToggleFavorite={handleToggleFavorite}
        onNewChat={handleNewChat}
        onDocumentSelectionChange={handleDocumentSelectionChange}
        onRenameSession={handleRenameSessionById}
      />

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Header Bar */}
        <div className="h-16 bg-white flex items-center justify-between px-6">
          <div className="flex items-center gap-4">
            {/* Mobile sidebar toggle */}
            <button
              onClick={toggleSidebar}
              className="md:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors"
            >
              {sidebarExpanded ? <ChevronLeft className="w-5 h-5" /> : <MessageSquare className="w-5 h-5" />}
            </button>
            {currentSession && isRenamingSession ? (
              <SessionRenameInput
                initialTitle={currentSession.title}
                onSave={handleRenameSession}
                onCancel={() => setIsRenamingSession(false)}
                className="flex-1 max-w-md"
              />
            ) : (
              <div className="flex items-center gap-2 group">
                <h1 
                  className="text-lg font-medium text-gray-900 cursor-pointer hover:text-blue-600 transition-colors"
                  onClick={() => currentSession && setIsRenamingSession(true)}
                  title="Click to rename session"
                >
                  {currentSession?.title || 'New Chat'}
                </h1>
                {currentSession && (
                  <button
                    onClick={() => setIsRenamingSession(true)}
                    className="opacity-0 group-hover:opacity-100 p-1 rounded hover:bg-gray-100 text-gray-500 hover:text-gray-700 transition-all duration-200"
                    title="Rename session"
                  >
                    <Edit2 className="w-4 h-4" />
                  </button>
                )}
              </div>
            )}
          </div>
          <div className="flex items-center gap-3">
            <button
              onClick={toggleDarkMode}
              className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
              title="Toggle theme"
            >
              {isDarkMode ? <Sun className="w-5 h-5" /> : <Moon className="w-5 h-5" />}
            </button>
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
              JD
            </div>
          </div>
        </div>

        {currentSession ? (
          <>
            {/* Messages Container */}
            <div className="flex-1 overflow-y-auto p-4">
              <div className="max-w-4xl mx-auto space-y-4">
                {currentSession.messages.map((message) => (
                  <MessageBubble
                    key={message.id}
                    message={message}
                    onCopy={() => handleMessageAction('copy', message)}
                    onRefresh={() => handleMessageAction('refresh', message)}
                    onThumbsUp={() => handleMessageAction('thumbsUp', message)}
                    onThumbsDown={() => handleMessageAction('thumbsDown', message)}
                  />
                ))}
                {isProcessing && (
                  <div className="flex justify-start mb-4">
                    <div className="bg-gray-100 rounded-lg px-4 py-3 border">
                      <div className="flex items-center gap-2 text-gray-600">
                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                        <span className="text-sm">Thinking Process<span className="animated-dots"></span></span>
                      </div>
                    </div>
                  </div>
                )}
                <div ref={messagesEndRef} />
              </div>
            </div>

            {/* Enhanced Input Area */}
            <div className="bg-white p-4">
              <div className="max-w-4xl mx-auto">
                <div className="relative">
                  <textarea
                    value={inputValue}
                    onChange={(e) => {
                      setInputValue(e.target.value)
                      autoResizeTextarea(e.target as HTMLTextAreaElement)
                    }}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault()
                        if (inputValue.trim()) {
                          handleSendMessage()
                          autoResizeTextarea(e.target as HTMLTextAreaElement)
                        }
                      }
                    }}
                    placeholder="Ask anything..."
                    className="w-full min-h-[2.5rem] max-h-24 resize-none rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 pl-12 pr-12 py-3 text-sm leading-5 outline-none transition-colors"
                    disabled={isLoading || isProcessing}
                  />
                  
                  {/* Attachment Button */}
                  <button
                    onClick={() => setAttachmentModalOpen(true)}
                    className="absolute bottom-2 left-2 p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded transition-colors"
                    disabled={isLoading || isProcessing}
                  >
                    <Paperclip className="w-4 h-4" />
                  </button>
                  
                  {/* Send Button */}
                  <button
                    onClick={() => {
                      if (inputValue.trim()) {
                        handleSendMessage()
                        const textarea = document.querySelector('textarea')
                        if (textarea) textarea.style.height = 'auto'
                      }
                    }}
                    disabled={!inputValue.trim() || isLoading || isProcessing}
                    className={`absolute bottom-2 right-2 p-1.5 rounded transition-colors ${
                      !inputValue.trim() || isLoading || isProcessing
                        ? 'text-gray-400 cursor-not-allowed'
                        : 'bg-blue-600 hover:bg-blue-700 text-white'
                    }`}
                  >
                    <ArrowUp className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          </>
        ) : (
          /* No Session - Document Selector */
          <div className="flex-1 flex items-center justify-center p-8">
            <div className="text-center max-w-md">
              <MessageCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Welcome to ChatDoc</h2>
              <p className="text-gray-600 mb-6">
                Start a new conversation by selecting documents or just begin chatting.
              </p>

              {documents.length > 0 ? (
                <div className="space-y-4">
                  <DocumentSelector
                    documents={documents}
                    selectedDocumentIds={selectedDocumentIds}
                    onSelectionChange={setSelectedDocumentIds}
                  />
                  <button
                    onClick={handleNewChat}
                    className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Start New Chat
                  </button>
                </div>
              ) : (
                <div className="space-y-4">
                  <p className="text-gray-500">No documents available. Upload some documents first.</p>
                  <button
                    onClick={handleNewChat}
                    className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Start Chat Without Documents
                  </button>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Mobile sidebar overlay */}
      {sidebarExpanded && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={() => setSidebarExpanded(false)}
        />
      )}
      
      {/* Attachment Modal */}
      <DocumentManagerModal
          isOpen={attachmentModalOpen}
          onClose={() => setAttachmentModalOpen(false)}
          documents={documents}
          selectedDocumentIds={selectedDocumentIds}
          onSelectionChange={handleDocumentSelectionChange}
          onDocumentsUpdate={handleDocumentsUpdate}
      />
    </div>
  )
}