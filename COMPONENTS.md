# Component Documentation

This document provides detailed information about the React components in ChatDoc v3.

## Architecture Overview

The application follows a component-driven architecture with clear separation of concerns:

- **Container Components**: Manage state and business logic
- **UI Components**: Pure presentation components
- **Service Integration**: Components that interface with services
- **Utility Components**: Reusable UI elements

## Core Components

### MainChatInterface (`src/components/MainChatInterface.tsx`)

**Purpose**: Primary application container managing overall state and layout

**Key Features**:
- Manages global application state
- Handles document and session persistence
- Coordinates service interactions
- Responsive layout management

**State Management**:
```typescript
interface AppState {
  documents: Document[]
  sessions: ChatSession[]
  currentSession: ChatSession | null
  isLoading: boolean
  selectedDocuments: string[]
}
```

**Key Functions**:
- `handleDocumentUpload()`: Processes new document uploads
- `handleSessionCreate()`: Creates new chat sessions
- `handleMessageSend()`: Processes user messages through RAG pipeline

### ChatSidebar (`src/components/ChatSidebar.tsx`)

**Purpose**: Navigation and management interface for sessions and documents

**Key Features**:
- Session list with search and filtering
- Document management interface
- Session favorites and organization
- Responsive collapse/expand behavior

**Props Interface**:
```typescript
interface ChatSidebarProps {
  sessions: ChatSession[]
  documents: Document[]
  currentSession: ChatSession | null
  onSessionSelect: (session: ChatSession) => void
  onSessionCreate: () => void
  onDocumentUpload: (files: FileList) => void
  isCollapsed: boolean
  onToggleCollapse: () => void
}
```

### ChatInput (`src/components/ChatInput.tsx`)

**Purpose**: Enhanced message input with document selection capabilities

**Key Features**:
- Multi-line text input with auto-resize
- Document selection integration
- Real-time typing indicators
- Keyboard shortcuts (Ctrl+Enter to send)

**Props Interface**:
```typescript
interface ChatInputProps {
  onSendMessage: (message: string) => void
  documents: Document[]
  selectedDocuments: string[]
  onDocumentSelectionChange: (documentIds: string[]) => void
  isLoading: boolean
}
```

### MessageBubble (`src/components/MessageBubble.tsx`)

**Purpose**: Displays chat messages with rich formatting and source attribution

**Key Features**:
- Markdown rendering with syntax highlighting
- Collapsible source citations
- Copy message functionality
- Reasoning display for AI responses

**Props Interface**:
```typescript
interface MessageBubbleProps {
  message: ChatMessage
  isLatest: boolean
  onCopy?: () => void
}
```

**Message Display Features**:
- Syntax highlighting for code blocks
- Responsive design for mobile devices
- Source similarity scores
- Expandable reasoning sections

### DocumentManagerModal (`src/components/DocumentManagerModal.tsx`)

**Purpose**: Comprehensive document management interface

**Key Features**:
- File upload with drag-and-drop support
- Document preview and metadata display
- Bulk operations (select all, delete multiple)
- Document search and filtering
- Rename functionality

**Props Interface**:
```typescript
interface DocumentManagerModalProps {
  isOpen: boolean
  onClose: () => void
  documents: Document[]
  onDocumentUpload: (files: FileList) => void
  onDocumentDelete: (documentId: string) => void
  onDocumentRename: (documentId: string, newName: string) => void
}
```

### UrlInput (`src/components/UrlInput.tsx`)

**Purpose**: Web content extraction interface with authentication support

**Key Features**:
- URL validation and formatting
- Basic authentication input
- Progress indicators during extraction
- Error handling and retry mechanisms

**Props Interface**:
```typescript
interface UrlInputProps {
  onExtract: (url: string, options?: WebExtractionOptions) => void
  isLoading: boolean
  error?: string
}
```

**Extraction Options**:
```typescript
interface WebExtractionOptions {
  maxContentLength?: number
  timeout?: number
  auth?: {
    username: string
    password: string
  }
}
```

## Utility Components

### DocumentSelector (`src/components/DocumentSelector.tsx`)

**Purpose**: Multi-select interface for choosing documents in chat context

**Key Features**:
- Search and filter documents
- Bulk selection controls
- Document metadata display
- Responsive grid layout

### LoadingIndicator (`src/components/LoadingIndicator.tsx`)

**Purpose**: Consistent loading states across the application

**Variants**:
- Spinner: General loading indicator
- Pulse: Content loading placeholder
- Progress: Determinate progress display

### DocumentRenameInput (`src/components/DocumentRenameInput.tsx`)

**Purpose**: Inline editing component for document names

**Key Features**:
- Click-to-edit functionality
- Validation and error handling
- Escape key cancellation
- Auto-focus and text selection

### SessionRenameInput (`src/components/SessionRenameInput.tsx`)

**Purpose**: Inline editing component for session titles

**Features**: Similar to DocumentRenameInput but optimized for session management

## Component Patterns

### State Management Pattern

Components use a combination of:
- **Local State**: Component-specific UI state (loading, editing, etc.)
- **Lifted State**: Shared state managed by parent components
- **Persistent State**: Data stored in localStorage through services

### Error Handling Pattern

All components implement consistent error handling:

```typescript
interface ComponentState {
  isLoading: boolean
  error: string | null
  data: any
}

// Error boundary wrapper for critical components
<ErrorBoundary fallback={<ErrorFallback />}>
  <ComponentWithPotentialErrors />
</ErrorBoundary>
```

### Loading States Pattern

Consistent loading indicators across components:

```typescript
// Loading state management
const [isLoading, setIsLoading] = useState(false)

// UI loading feedback
{isLoading ? <LoadingIndicator /> : <ComponentContent />}
```

### Event Handling Pattern

Consistent event handling with error boundaries:

```typescript
const handleAction = async (data: any) => {
  try {
    setIsLoading(true)
    await serviceFunction(data)
    // Success handling
  } catch (error) {
    setError(error.message)
  } finally {
    setIsLoading(false)
  }
}
```

## Styling Guidelines

### Tailwind CSS Usage

Components use utility-first Tailwind classes:

- **Layout**: `flex`, `grid`, `space-y-4`, `gap-4`
- **Typography**: `text-sm`, `font-medium`, `text-gray-600`
- **Colors**: Custom palette with `primary-*` and `gray-*` variants
- **Responsive**: `sm:`, `md:`, `lg:` prefixes for breakpoints
- **Dark Mode**: `dark:` prefix for dark mode variants

### Component Class Patterns

```typescript
// Base component classes
const baseClasses = "rounded-lg border border-gray-200 p-4"

// Interactive states
const buttonClasses = "hover:bg-gray-50 active:bg-gray-100 transition-colors"

// Responsive design
const responsiveClasses = "w-full md:w-auto lg:max-w-md"
```

### Custom CSS Properties

Components leverage CSS custom properties for theming:

```css
:root {
  --primary-50: #eff6ff;
  --primary-600: #2563eb;
  --gray-50: #f9fafb;
  --gray-900: #111827;
}
```

## Accessibility

### ARIA Implementation

Components include proper ARIA attributes:

- **Landmarks**: `role="main"`, `role="navigation"`
- **Labels**: `aria-label`, `aria-labelledby`
- **States**: `aria-expanded`, `aria-selected`
- **Descriptions**: `aria-describedby`

### Keyboard Navigation

All interactive components support keyboard navigation:

- **Tab Order**: Logical tab sequence
- **Enter/Space**: Action triggers
- **Escape**: Modal and dropdown dismissal
- **Arrow Keys**: List navigation

### Screen Reader Support

Components provide screen reader compatibility:

- **Focus Management**: Proper focus handling
- **Live Regions**: Dynamic content announcements
- **Skip Links**: Navigation shortcuts
- **Alt Text**: Image descriptions

## Testing Strategies

### Component Testing

Each component includes comprehensive tests:

```typescript
// Example test structure
describe('MessageBubble', () => {
  it('renders user messages correctly', () => {})
  it('displays source citations for AI responses', () => {})
  it('handles copy functionality', () => {})
  it('expands/collapses reasoning sections', () => {})
})
```

### Integration Testing

Components are tested in realistic usage scenarios:

- **User Workflows**: Complete interaction flows
- **Service Integration**: API communication
- **State Management**: Data flow validation
- **Error Scenarios**: Error handling verification

### Visual Testing

Components maintain visual consistency:

- **Responsive Design**: Multiple viewport testing
- **Browser Compatibility**: Cross-browser validation
- **Theme Testing**: Light/dark mode verification
- **Accessibility Testing**: Screen reader compatibility

## Performance Optimization

### React Optimization

Components use React performance patterns:

```typescript
// Memoization for expensive calculations
const expensiveValue = useMemo(() => 
  calculateComplexValue(props.data), [props.data]
)

// Callback memoization
const handleClick = useCallback(() => {
  // Click handler logic
}, [dependency])

// Component memoization
const OptimizedComponent = React.memo(Component)
```

### Rendering Optimization

- **Virtual Scrolling**: For large document lists
- **Lazy Loading**: Deferred component loading
- **Code Splitting**: Dynamic imports for modal components
- **Image Optimization**: Responsive image loading

### Memory Management

Components implement proper cleanup:

```typescript
useEffect(() => {
  const subscription = subscribe(callback)
  
  return () => {
    subscription.unsubscribe()
  }
}, [])
```