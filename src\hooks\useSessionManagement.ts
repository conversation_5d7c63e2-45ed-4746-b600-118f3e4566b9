import { useState, useEffect } from 'react'
import { ChatSession } from '@/types'
import {
  getAllSessions,
  createSession,
  deleteSession,
  toggleSessionFavorite,
  renameSession
} from '@/services/chatService'

export function useSessionManagement() {
  const [sessions, setSessions] = useState<ChatSession[]>([])
  const [currentSession, setCurrentSession] = useState<ChatSession | null>(null)
  const [isRenamingSession, setIsRenamingSession] = useState(false)

  // Load sessions on mount
  useEffect(() => {
    const loadedSessions = getAllSessions()
    setSessions(loadedSessions)
    
    // Set current session to the most recent one if available
    if (loadedSessions.length > 0 && !currentSession) {
      setCurrentSession(loadedSessions[0])
    }
  }, [])

  const handleNewChat = () => {
    const newSession = createSession()
    setSessions(prev => [newSession, ...prev])
    setCurrentSession(newSession)
  }

  const handleSessionSelect = (session: ChatSession) => {
    setCurrentSession(session)
  }

  const handleDeleteSession = (sessionId: string) => {
    deleteSession(sessionId)
    const updatedSessions = getAllSessions()
    setSessions(updatedSessions)
    
    // If we deleted the current session, switch to another one
    if (currentSession?.id === sessionId) {
      setCurrentSession(updatedSessions.length > 0 ? updatedSessions[0] : null)
    }
  }

  const handleToggleFavorite = (sessionId: string) => {
    toggleSessionFavorite(sessionId)
    const updatedSessions = getAllSessions()
    setSessions(updatedSessions)

    // Update current session if it was the one modified
    if (currentSession?.id === sessionId) {
      const updatedCurrentSession = updatedSessions.find(s => s.id === sessionId)
      if (updatedCurrentSession) {
        setCurrentSession(updatedCurrentSession)
      }
    }
  }

  const handleRenameSession = (sessionId: string, newTitle: string) => {
    renameSession(sessionId, newTitle)
    const updatedSessions = getAllSessions()
    setSessions(updatedSessions)
    
    // Update current session if it was the one renamed
    if (currentSession?.id === sessionId) {
      const updatedCurrentSession = updatedSessions.find(s => s.id === sessionId)
      if (updatedCurrentSession) {
        setCurrentSession(updatedCurrentSession)
      }
    }
    setIsRenamingSession(false)
  }

  const refreshSessions = () => {
    const updatedSessions = getAllSessions()
    setSessions(updatedSessions)
    
    // Update current session data
    if (currentSession) {
      const updatedCurrentSession = updatedSessions.find(s => s.id === currentSession.id)
      if (updatedCurrentSession) {
        setCurrentSession(updatedCurrentSession)
      }
    }
  }

  return {
    sessions,
    currentSession,
    isRenamingSession,
    setIsRenamingSession,
    handleNewChat,
    handleSessionSelect,
    handleDeleteSession,
    handleToggleFavorite,
    handleRenameSession,
    refreshSessions
  }
}
