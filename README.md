# ChatDoc - AI Document Chat Assistant

A modern React application that allows users to upload documents and chat with them using AI. Built with TypeScript, Vite, and Tailwind CSS, featuring client-side RAG (Retrieval-Augmented Generation) for intelligent document interaction.

## Features

- 📄 **Multiple Document Formats**: Support for TXT, MD files and web content
- 🌐 **Web Content Extraction**: Extract and process content from web URLs with retry mechanisms
- 💬 **Natural Language Chat**: Ask questions about your documents with AI-powered responses
- 🧠 **RAG Implementation**: Client-side vector embeddings and similarity search
- 📚 **Document Management**: Upload, rename, delete, and organize documents
- 💾 **Session Management**: Persistent chat sessions with full conversation history
- 🎯 **Source Attribution**: Responses include citations with similarity scores
- ⚡ **Fast & Responsive**: Built with modern web technologies and optimized performance
- 🎨 **Beautiful UI**: Clean, modern interface with Tailwind CSS and dark mode support
- 🔒 **Type Safe**: Full TypeScript support with strict configuration

## Tech Stack

- **Frontend**: React 18 + TypeScript
- **Build Tool**: Vite with custom proxy configuration
- **Styling**: Tailwind CSS with custom color palette
- **State Management**: React hooks with localStorage persistence
- **AI Integration**: Configurable LLM and embedding APIs (DeepSeek, OpenAI compatible)
- **Document Processing**: Client-side text extraction and chunking
- **Vector Search**: Cosine similarity with configurable thresholds
- **Icons**: Lucide React
- **UI Components**: React Markdown, Syntax Highlighter
- **Notifications**: Sonner
- **Testing**: Vitest
- **Linting**: ESLint with TypeScript rules

## Project Structure

```
src/
├── components/          # React UI components
│   ├── ChatInput.tsx           # Enhanced chat input with document selection
│   ├── ChatSidebar.tsx         # Session management and document list
│   ├── DocumentManagerModal.tsx # Document upload and management
│   ├── DocumentRenameInput.tsx  # Inline document renaming
│   ├── DocumentSelector.tsx    # Multi-document selection interface
│   ├── LoadingIndicator.tsx    # Consistent loading states
│   ├── MainChatInterface.tsx   # Main application interface
│   ├── MessageBubble.tsx       # Chat message display with sources
│   ├── SessionRenameInput.tsx  # Inline session renaming
│   └── UrlInput.tsx            # Web URL content extraction
├── services/           # Business logic and API integration
│   ├── chatService.ts          # RAG implementation and LLM integration
│   ├── documentApiService.ts   # Document CRUD operations
│   ├── embeddingService.ts     # Vector embeddings and similarity search
│   └── webExtractionService.ts # Web content extraction with retry logic
├── types/              # TypeScript type definitions
│   └── index.ts               # Document, Chat, and API types
├── utils/              # Utility functions
│   ├── cn.ts                  # Tailwind class name utilities
│   ├── debugUtils.ts          # Development debugging tools
│   ├── documentUtils.ts       # Document processing utilities
│   └── fileUtils.ts           # File handling utilities
├── App.tsx             # Main app component with error boundary
├── main.tsx           # App entry point
├── index.css          # Global Tailwind styles
└── vite-env.d.ts      # Vite environment types
```

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd chatdoc-v3
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:3000`

### Available Scripts

- `npm run dev` - Start development server with proxy (http://localhost:5173)
- `npm run dev:vite` - Start Vite development server only
- `npm run dev:proxy` - Start proxy server only
- `npm run build` - Build for production (TypeScript compilation + Vite build)
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint with TypeScript support
- `npm run test` - Run Vitest test suite
- `npm run test:ui` - Run tests with UI interface

### Environment Configuration

The application requires API configuration for AI services. Create a `.env` file with:

```env
# LLM/Reasoning API Configuration
VITE_REASONING_API_URL=your_llm_api_endpoint
VITE_REASONING_API_KEY=your_llm_api_key
VITE_REASONING_MODEL=your_llm_model_name

# Embedding API Configuration
VITE_EMBEDDING_API_URL=your_embedding_api_endpoint
VITE_EMBEDDING_API_KEY=your_embedding_api_key
VITE_EMBEDDING_MODEL=your_embedding_model_name
```

**Supported APIs:**
- **LLM**: DeepSeek, OpenAI, or any OpenAI-compatible API
- **Embeddings**: Doubao, OpenAI, or any compatible embedding API

### Proxy Configuration

The application includes a proxy server (`proxy-server.js`) for web content extraction:

- **Port**: 9527 (configurable)
- **Purpose**: Bypass CORS restrictions for web content extraction
- **Features**: Timeout handling, authentication forwarding, error recovery
- **Endpoints**: `/api/web-proxy` for URL content fetching

The Vite development server proxies API requests:
- `/api/reasoning` → LLM API endpoint
- `/api/embedding` → Embedding API endpoint  
- `/api/web-proxy` → Local proxy server (port 9527)

## Development Guidelines

### Code Style

- Use TypeScript for all files with strict configuration
- Follow React functional component patterns with hooks
- Use Tailwind CSS utility classes for styling
- Implement comprehensive error handling with boundaries
- Use meaningful component and function names with clear interfaces
- Prefer composition over inheritance
- Use path aliases (`@/`) for internal imports

### Component Organization

- **Components**: UI components with single responsibility
- **Services**: Business logic and external API integration
- **Utils**: Pure utility functions without side effects
- **Types**: Comprehensive TypeScript interfaces and types
- **State Management**: React hooks with localStorage persistence

### API Integration Patterns

- **Configurable Providers**: Support multiple LLM and embedding APIs
- **Error Handling**: Graceful fallbacks and user-friendly error messages
- **Timeout Management**: Configurable timeouts for API calls
- **Retry Logic**: Exponential backoff for web extraction
- **Type Safety**: Full typing for API requests and responses

### Storage Strategy

- **Documents**: Stored in localStorage with metadata
- **Embeddings**: Cached with document chunks for performance
- **Sessions**: Persistent chat history with document associations
- **Settings**: User preferences and API configurations

### Architecture Overview

#### Client-Side RAG Implementation
```
Document Upload → Text Extraction → Chunking → Embedding Generation → Vector Storage (localStorage)
                                                        ↓
User Query → Query Embedding → Similarity Search → Context Assembly → LLM Request → Response
```

#### Service Layer Architecture
- **ChatService**: Orchestrates RAG flow, manages chat sessions, integrates with LLM APIs
- **EmbeddingService**: Handles document chunking, embedding generation, and similarity search
- **DocumentApiService**: Manages document CRUD operations and storage
- **WebExtractionService**: Extracts and processes content from web URLs

#### Component Architecture
- **MainChatInterface**: Primary application container managing state and layout
- **ChatSidebar**: Session management, document list, and navigation
- **Chat Components**: Message display, input handling, and document selection
- **Document Components**: Upload, management, and URL extraction interfaces

### Naming Conventions

- Components: PascalCase (e.g., `MainChatInterface.tsx`)
- Services: camelCase with descriptive names (e.g., `chatService.ts`)
- Functions: camelCase with action-oriented names
- Constants: UPPER_SNAKE_CASE
- Types/Interfaces: PascalCase with descriptive names
- CSS Classes: Tailwind utility classes

## Current Features

- [x] Document upload functionality (TXT, MD files)
- [x] Web URL content extraction with retry mechanisms
- [x] AI chat integration with RAG implementation
- [x] Document management (upload, rename, delete)
- [x] Chat history with persistent sessions
- [x] Document search and similarity-based retrieval
- [x] Source attribution with similarity scores
- [x] Session management with favorites
- [x] Responsive UI with dark mode support

## Future Enhancements

- [ ] PDF and DOCX file support
- [ ] User authentication and multi-user support
- [ ] Export conversations (PDF, Markdown)
- [ ] Advanced document search and filtering
- [ ] Chat session sharing
- [ ] Custom embedding model support
- [ ] Document version control
- [ ] Advanced RAG configurations

## Contributing

### Development Workflow

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Set up your environment with the required API keys
4. Make your changes following the code style guidelines
5. Add tests if applicable
6. Run `npm run lint` to check for code style issues
7. Run `npm run test` to ensure all tests pass
8. Commit your changes (`git commit -m 'Add amazing feature'`)
9. Push to your branch (`git push origin feature/amazing-feature`)
10. Submit a pull request

### Testing Guidelines

- Write unit tests for service layer functions
- Test error handling scenarios
- Verify API integration with mock data
- Test component interactions and state management

### Code Review Checklist

- [ ] TypeScript types are properly defined
- [ ] Error handling is implemented
- [ ] Component props are properly typed
- [ ] API calls include timeout and error handling
- [ ] localStorage operations are wrapped in try-catch
- [ ] UI is responsive and accessible
- [ ] Code follows naming conventions

## License

MIT License - see LICENSE file for details