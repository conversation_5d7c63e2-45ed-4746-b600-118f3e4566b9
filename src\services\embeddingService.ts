import { Document, DocumentChunk } from '@/types'

/**
 * Simplified embedding service for document chunking and vector operations
 */

const CHUNK_SIZE = 800
const CHUNK_OVERLAP = 200
const MIN_CHUNK_LENGTH = 50

/**
 * Check if embedding API is configured
 */
export function isEmbeddingApiConfigured(): { configured: boolean; message: string } {
  const apiUrl = import.meta.env.VITE_EMBEDDING_API_URL
  const apiKey = import.meta.env.VITE_EMBEDDING_API_KEY

  if (!apiUrl || !apiKey) {
    return {
      configured: false,
      message: 'Embedding API not configured'
    }
  }

  return {
    configured: true,
    message: 'Embedding API configured'
  }
}


/**
 * Split document content into chunks with overlap
 */
export function chunkDocument(document: Document | string): DocumentChunk[] {
  // Handle both Document object and string input
  const content = typeof document === 'string' ? document : document.content
  const id = typeof document === 'string' ? 'test-doc' : document.id
  const name = typeof document === 'string' ? 'Test Document' : document.name
  const chunks: DocumentChunk[] = []
  const step = CHUNK_SIZE - CHUNK_OVERLAP
  
  for (let i = 0; i < content.length; i += step) {
    const chunkContent = content.slice(i, i + CHUNK_SIZE).trim()
    
    if (chunkContent.length >= MIN_CHUNK_LENGTH) {
      chunks.push({
        id: `${id}-chunk-${chunks.length}`,
        documentId: id,
        documentName: name,
        content: chunkContent,
        startIndex: i,
        endIndex: Math.min(i + CHUNK_SIZE, content.length)
      })
    }
  }
  
  return chunks
}
  
/**
 * Generate embedding for text using embedding API
 */
export async function generateEmbedding(text: string): Promise<{ embedding: number[] }> {
  const apiUrl = import.meta.env.VITE_EMBEDDING_API_URL
  const apiKey = import.meta.env.VITE_EMBEDDING_API_KEY
  const model = import.meta.env.VITE_EMBEDDING_MODEL || 'text-embedding-ada-002'

  if (!apiUrl || !apiKey) {
    console.warn('Embedding API not configured, using mock embedding')
    return { embedding: mockEmbedding() }
  }

  if (!text?.trim()) {
    return { embedding: mockEmbedding() }
  }

  // Truncate text if too long
  const maxLength = 8000
  const truncatedText = text.length > maxLength ? text.substring(0, maxLength) : text
  
  try {
    // Create abort controller for timeout
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 15000) // 15 second timeout
    
    const response = await fetch('/api/embedding', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        input: truncatedText,
        model: model
      }),
      signal: controller.signal
    })
    
    clearTimeout(timeoutId)
    
    if (!response.ok) {
      console.error(`Embedding API error: ${response.status}`)
      return { embedding: mockEmbedding() }
    }

    const data = await response.json()

    if (data.data?.[0]?.embedding) {
      return { embedding: data.data[0].embedding }
    }

    return { embedding: mockEmbedding() }
  } catch (error) {
    console.error('Error generating embedding:', error)
    return { embedding: mockEmbedding() }
  }
}
  
/**
 * Generate simple mock embedding for development
 */
function mockEmbedding(): number[] {
  // Simple normalized random embedding (384 dimensions)
  return new Array(384).fill(0).map(() => (Math.random() - 0.5) * 0.1)
}
  
/**
 * Process document and generate embeddings for all chunks
 */
export async function processDocument(document: Document): Promise<void> {
  try {
    const chunks = chunkDocument(document)
    
    if (chunks.length === 0) {
      console.warn('No chunks generated for document:', document.name)
      return
    }
    
    console.log(`Processing ${chunks.length} chunks for document: ${document.name}`)
    
    // Check if API configuration is available
    const apiUrl = import.meta.env.VITE_EMBEDDING_API_URL
    const apiKey = import.meta.env.VITE_EMBEDDING_API_KEY
    
    if (!apiUrl || !apiKey) {
      console.warn('Embedding API not configured, using mock embeddings for development')
      // Use mock embeddings when API is not configured
      for (const chunk of chunks) {
        chunk.embedding = mockEmbedding()
      }
    } else {
      // Process chunks with real API
      const processedChunks: DocumentChunk[] = []
      
      for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i]
        try {
          console.log(`Processing chunk ${i + 1}/${chunks.length} for ${document.name}`)
          const result = await generateEmbedding(chunk.content)
          chunk.embedding = result.embedding
          processedChunks.push(chunk)
        } catch (error) {
          console.error(`Failed to generate embedding for chunk ${i + 1}:`, error)
          // Use mock embedding as fallback
          chunk.embedding = mockEmbedding()
          processedChunks.push(chunk)
        }
      }
      
      // Update chunks array with processed chunks
      chunks.splice(0, chunks.length, ...processedChunks)
    }
    
    // Store embeddings with validation
    storeEmbeddings(chunks)
    console.log(`Successfully processed and stored embeddings for document: ${document.name}`)
  } catch (error) {
    console.error('Error processing document:', error)
    throw new Error(`Failed to process document ${document.name}: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}
  
/**
 * Calculate cosine similarity between two vectors
 */
export function cosineSimilarity(a: number[], b: number[]): number {
    if (a.length !== b.length) return 0
    
    const dotProduct = a.reduce((sum, val, i) => sum + val * b[i], 0)
    const normA = Math.sqrt(a.reduce((sum, val) => sum + val * val, 0))
    const normB = Math.sqrt(b.reduce((sum, val) => sum + val * val, 0))
    
    return dotProduct / (normA * normB)
  }
  




/**
 * Store document embeddings with deduplication
 */
export function storeEmbeddings(chunks: DocumentChunk[]): void {
  try {
    // Validate input chunks
    if (!Array.isArray(chunks)) {
      console.error('Invalid chunks data: expected array, got:', typeof chunks)
      return
    }

    // Validate each chunk structure
    const validChunks = chunks.filter(chunk => {
      if (!chunk || typeof chunk !== 'object') {
        console.warn('Invalid chunk: not an object', chunk)
        return false
      }
      if (!chunk.id || !chunk.documentId || !chunk.content) {
        console.warn('Invalid chunk: missing required fields', chunk)
        return false
      }
      if (!Array.isArray(chunk.embedding) || chunk.embedding.length === 0) {
        console.warn('Invalid chunk: missing or invalid embedding', chunk.id)
        return false
      }
      return true
    })

    if (validChunks.length !== chunks.length) {
      console.warn(`Filtered out ${chunks.length - validChunks.length} invalid chunks`)
    }

    if (validChunks.length === 0) {
      console.warn('No valid chunks to store')
      return
    }

    // Get existing embeddings and remove chunks for the same documents (deduplication)
    const existingEmbeddings = getStoredEmbeddings()
    const documentIds = new Set(validChunks.map(chunk => chunk.documentId))
    const filteredExisting = existingEmbeddings.filter(chunk => !documentIds.has(chunk.documentId))

    // Combine filtered existing with new chunks
    const updatedEmbeddings = [...filteredExisting, ...validChunks]

    // Store embeddings
    localStorage.setItem('chatdoc-embeddings', JSON.stringify(updatedEmbeddings))
    console.log(`Successfully stored ${validChunks.length} embedding chunks (${updatedEmbeddings.length} total)`)
  } catch (error) {
    console.error('Error storing embeddings:', error)
    // Simple error handling for quota exceeded
    if (error instanceof Error && (error.name === 'QuotaExceededError' || error.message.includes('QuotaExceededError'))) {
      console.warn('localStorage quota exceeded. Consider clearing old documents.')
    }
  }
}
  
/**
 * Get all stored embeddings
 */
export function getStoredEmbeddings(): DocumentChunk[] {
  try {
    const data = localStorage.getItem('chatdoc-embeddings')
    if (!data) {
      return []
    }

    const parsed = JSON.parse(data)

    // Validate the parsed data structure
    if (!Array.isArray(parsed)) {
      console.warn('Stored embeddings data is not an array, resetting')
      localStorage.removeItem('chatdoc-embeddings')
      return []
    }

    // Filter out corrupted chunks
    const validChunks = parsed.filter(chunk => {
      if (!chunk || typeof chunk !== 'object') {
        return false
      }
      if (!chunk.id || !chunk.documentId || !chunk.content) {
        return false
      }
      if (!Array.isArray(chunk.embedding)) {
        return false
      }
      return true
    })

    // If we filtered out corrupted data, save the cleaned version
    if (validChunks.length !== parsed.length) {
      console.warn(`Cleaned ${parsed.length - validChunks.length} corrupted embedding chunks`)
      try {
        localStorage.setItem('chatdoc-embeddings', JSON.stringify(validChunks))
      } catch (saveError) {
        console.error('Failed to save cleaned embeddings:', saveError)
      }
    }

    return validChunks
  } catch (error) {
    console.error('Error loading embeddings, clearing corrupted data:', error)
    try {
      localStorage.removeItem('chatdoc-embeddings')
    } catch (clearError) {
      console.error('Failed to clear corrupted embeddings:', clearError)
    }
    return []
  }
}

/**
 * Get stored embeddings for a specific document
 */
export function getStoredEmbeddingsForDocument(documentId: string): DocumentChunk[] {
  try {
    // Use the unified storage approach instead of document-specific keys
    const allEmbeddings = getStoredEmbeddings()
    return allEmbeddings.filter(chunk => chunk.documentId === documentId)
  } catch (error) {
    console.error('Error retrieving stored embeddings for document:', error)
    return []
  }
}
  
/**
 * Delete stored embeddings for a specific document
 */
export function deleteStoredEmbeddings(documentId: string): void {
  try {
    const allEmbeddings = getStoredEmbeddings()
    const filteredEmbeddings = allEmbeddings.filter(chunk => chunk.documentId !== documentId)

    if (filteredEmbeddings.length === 0) {
      // If no embeddings left, remove the key entirely
      localStorage.removeItem('chatdoc-embeddings')
      console.log(`Removed all embeddings (deleted document ${documentId})`)
    } else {
      // Store the filtered embeddings
      localStorage.setItem('chatdoc-embeddings', JSON.stringify(filteredEmbeddings))
      console.log(`Deleted embeddings for document ${documentId} (${allEmbeddings.length - filteredEmbeddings.length} chunks removed)`)
    }
  } catch (error) {
    console.error('Error deleting stored embeddings:', error)
  }
}