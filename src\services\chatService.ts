import { ChatMessage, ChatSession, DocumentChunk, SearchResult } from '@/types'
import { generateEmbedding, getStoredEmbeddings, cosineSimilarity } from './embeddingService'

/**
 * Simplified chat service for RAG logic and LLM integration
 */

const CHAT_STORAGE_KEY = 'chatdoc-sessions'
const MAX_CONTEXT_CHUNKS = 5
const SIMILARITY_THRESHOLD = 0.3
/**
 * Search for relevant document chunks using vector similarity
 */
export async function searchRelevantChunks(
  query: string,
  documentIds: string[]
): Promise<SearchResult[]> {
  try {
    const queryEmbedding = await generateEmbedding(query)
    if (!queryEmbedding.embedding) return []
  
  const allEmbeddings = getStoredEmbeddings()
  const allChunks = documentIds.flatMap(id => 
    allEmbeddings.filter(chunk => chunk.documentId === id)
  )
  
  return allChunks
      .filter(chunk => chunk.embedding)
      .map(chunk => ({
        chunk,
        similarity: cosineSimilarity(queryEmbedding.embedding!, chunk.embedding!)
      }))
      .filter(result => result.similarity > SIMILARITY_THRESHOLD)
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, MAX_CONTEXT_CHUNKS)
  } catch (error) {
    console.error('Error searching relevant chunks:', error)
    return []
  }
}
  
/**
 * Generate response using reasoning API
 */
export async function generateResponse(
  query: string,
  context: DocumentChunk[],
  chatHistory: ChatMessage[] = []
): Promise<{ content: string; error?: string }> {
    const apiUrl = import.meta.env.VITE_REASONING_API_URL || 'https://ark.cn-beijing.volces.com/api/v3/chat/completions'
    const apiKey = import.meta.env.VITE_REASONING_API_KEY || ''
    const model = import.meta.env.VITE_REASONING_MODEL || 'deepseek-r1-distill-qwen-32b-250120'

    if (!apiKey) {
      return { content: "API not configured. Please set up your reasoning API credentials." }
    }
    
    let systemPrompt: string
    
    // Check if documents are available for RAG
    if (context && context.length > 0) {
      // RAG mode: Use document context
      const contextText = context
        .map(chunk => `[Document: ${chunk.documentName}]\n${chunk.content}`)
        .join('\n\n')
      
      const historyText = chatHistory
        .slice(-6)
        .map(msg => `${msg.role === 'user' ? 'User' : 'Assistant'}: ${msg.content}`)
        .join('\n')

      systemPrompt = `You are a helpful AI assistant that answers questions based on provided document context. Use the following documents to answer the user's question. If the answer cannot be found in the provided context, say so clearly.

Document Context:
${contextText}

${historyText ? `Previous Conversation:\n${historyText}\n` : ''}`
    } else {
      // Direct LLM mode: No document context
      const historyText = chatHistory
        .slice(-6)
        .map(msg => `${msg.role === 'user' ? 'User' : 'Assistant'}: ${msg.content}`)
        .join('\n')

      systemPrompt = `You are a helpful AI assistant. Provide accurate and helpful responses to the user's questions.

${historyText ? `Previous Conversation:\n${historyText}\n` : ''}`
    }
    
    try {
      const response = await fetch('/api/reasoning', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`,
          'X-Target-Url': apiUrl
        },
        body: JSON.stringify({
          model: model,
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: query }
          ],
          temperature: 0.7,
          max_tokens: 1000
        })
      })
      
      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`)
      }
      
      const data = await response.json()
      return { content: data.choices[0].message.content }
    } catch (error) {
      console.error('Error generating response:', error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      return { 
        content: "Sorry, I encountered an error while generating a response. Please try again.",
        error: errorMessage 
      }
    }
  }
  

  
/**
 * Process a chat message and generate response
 */
export async function processMessage(
  sessionId: string,
  userMessage: string,
  documentIds?: string[]
): Promise<ChatMessage> {
  const session = getSession(sessionId)
    const targetDocumentIds = documentIds || session?.documentIds || []
    
    const searchResults = await searchRelevantChunks(userMessage, targetDocumentIds)
    const relevantChunks = searchResults.map(result => result.chunk)
    
    const llmResponse = await generateResponse(
      userMessage,
      relevantChunks,
      session?.messages || []
    )
    
    return {
      id: Date.now().toString(),
      role: 'assistant',
      content: llmResponse.content,
      timestamp: new Date(),
      searchResults: searchResults
    }
  }
  
/**
 * Create a new chat session
 */
export function createSession(documentIds?: string[], title?: string): ChatSession {
  const session: ChatSession = {
    id: Date.now().toString(),
    title: title || 'New Chat',
    documentIds: documentIds || [],
    isFavorite: false,
    messages: [],
    createdAt: new Date(),
    updatedAt: new Date()
  }
  
  saveSession(session)
  return session
}
  
/**
 * Add message to session
 */
export function addMessageToSession(sessionId: string, message: ChatMessage): void {
  const session = getSession(sessionId)
  if (!session) return
  
  session.messages.push(message)
  session.updatedAt = new Date()
  
  // Auto-generate title from first user message
  if (session.messages.length === 1 && message.role === 'user') {
    session.title = message.content.slice(0, 50) + (message.content.length > 50 ? '...' : '')
  }
  
  saveSession(session)
}
  
/**
 * Convert session dates from strings to Date objects
 */
function convertSessionDates(session: any): ChatSession {
  return {
    ...session,
    createdAt: new Date(session.createdAt),
    updatedAt: new Date(session.updatedAt),
    messages: session.messages.map((msg: any) => ({
      ...msg,
      timestamp: new Date(msg.timestamp)
    }))
  }
}

/**
 * Get session by ID
 */
export function getSession(sessionId: string): ChatSession | null {
  try {
    const data = localStorage.getItem(CHAT_STORAGE_KEY)
    const sessions = data ? JSON.parse(data) : {}
    const session = sessions[sessionId]
    if (!session) return null
    
    return convertSessionDates(session)
  } catch {
    return null
  }
}
  
/**
 * Save session
 */
export function saveSession(session: ChatSession): void {
  try {
    const data = localStorage.getItem(CHAT_STORAGE_KEY)
    const sessions = data ? JSON.parse(data) : {}
    sessions[session.id] = session
    localStorage.setItem(CHAT_STORAGE_KEY, JSON.stringify(sessions))
  } catch (error) {
    console.error('Failed to save session:', error)
  }
}
  
/**
 * Get all sessions
 */
export function getAllSessions(): ChatSession[] {
  try {
    const data = localStorage.getItem(CHAT_STORAGE_KEY)
    const sessions = data ? JSON.parse(data) : {}
    return Object.values(sessions).map(session => convertSessionDates(session))
  } catch {
    return []
  }
}
  
/**
 * Delete session
 */
export function deleteSession(sessionId: string): void {
  try {
    const data = localStorage.getItem(CHAT_STORAGE_KEY)
    const sessions = data ? JSON.parse(data) : {}
    delete sessions[sessionId]
    localStorage.setItem(CHAT_STORAGE_KEY, JSON.stringify(sessions))
  } catch (error) {
    console.error('Failed to delete session:', error)
  }
}

/**
 * Update session document IDs
 */
export function updateSessionDocuments(sessionId: string, documentIds: string[]): void {
  const session = getSession(sessionId)
  if (!session) return
  
  session.documentIds = documentIds
  session.updatedAt = new Date()
  saveSession(session)
}

/**
 * Add or remove documents from a session
 */
export function addDocumentToSession(sessionId: string, documentId: string): void {
  const session = getSession(sessionId)
  if (!session) return
  
  if (!session.documentIds.includes(documentId)) {
    session.documentIds.push(documentId)
    session.updatedAt = new Date()
    saveSession(session)
  }
}

export function removeDocumentFromSession(sessionId: string, documentId: string): void {
  const session = getSession(sessionId)
  if (!session) return
  
  session.documentIds = session.documentIds.filter(id => id !== documentId)
  session.updatedAt = new Date()
  saveSession(session)
}

/**
 * Toggle favorite status of a session
 */
export function toggleSessionFavorite(sessionId: string): void {
  const session = getSession(sessionId)
  if (!session) return
  
  session.isFavorite = !session.isFavorite
  session.updatedAt = new Date()
  saveSession(session)
}

/**
 * Rename a chat session
 */
export function renameSession(sessionId: string, newTitle: string): boolean {
  const session = getSession(sessionId)
  if (!session) return false
  
  // Validate the new title
  const trimmedTitle = newTitle.trim()
  if (!trimmedTitle || trimmedTitle.length === 0) {
    return false
  }
  
  // Limit title length to prevent UI issues
  const maxTitleLength = 100
  const finalTitle = trimmedTitle.length > maxTitleLength 
    ? trimmedTitle.substring(0, maxTitleLength).trim() 
    : trimmedTitle
  
  // Update the session
  session.title = finalTitle
  session.updatedAt = new Date()
  saveSession(session)
  
  return true
}

/**
 * Get all favorite sessions
 */
export function getFavoriteSessions(): ChatSession[] {
  return getAllSessions().filter(session => session.isFavorite)
}

/**
 * Get sessions sorted by favorites first, then by update date
 */
export function getSessionsSortedByFavorites(): ChatSession[] {
  const sessions = getAllSessions()
  return sessions.sort((a, b) => {
    // Favorites first
    if (a.isFavorite && !b.isFavorite) return -1
    if (!a.isFavorite && b.isFavorite) return 1
    // Then by update date (newest first)
    return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
  })
}