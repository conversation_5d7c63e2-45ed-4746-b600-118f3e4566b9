import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    port: 3000,
    open: true,
    proxy: {
      '/api/web-proxy': {
        target: 'http://localhost:9527',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/api\/web-proxy/, '/api/web-proxy')
      }
    }
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
  },
})