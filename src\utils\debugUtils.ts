/**
 * Debug utilities for troubleshooting the ChatDoc application
 */

import { getDocuments } from './documentUtils'
import { getStoredEmbeddings } from '../services/embeddingService'

/**
 * Debug function to check document and embedding status
 */
export function debugChatDoc() {
  console.log('🔍 ChatDoc Debug Information');

  try {
    const documents = getDocuments();
    const embeddings = getStoredEmbeddings();

    console.log(`📄 Documents: ${documents.length}`);
    console.log(`🧠 Embedding Chunks: ${embeddings.length}`);

    // Check for orphaned embeddings
    const documentIds = new Set(documents.map(doc => doc.id));
    const orphanedEmbeddings = embeddings.filter(chunk => !documentIds.has(chunk.documentId));

    if (orphanedEmbeddings.length > 0) {
      console.log(`⚠️  Orphaned embeddings: ${orphanedEmbeddings.length}`);
    }

    return {
      documents: documents.length,
      embeddings: embeddings.length,
      orphanedEmbeddings: orphanedEmbeddings.length
    };

  } catch (error) {
    console.error('❌ Debug function failed:', error);
    return { error: error instanceof Error ? error.message : 'Unknown error' };
  }
}


