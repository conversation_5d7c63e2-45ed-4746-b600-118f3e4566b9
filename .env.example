# =============================================================================
# API CONFIGURATION - Replace placeholder values with real API keys
# =============================================================================

# LLM API Configuration (for chat/reasoning functionality)
VITE_REASONING_API_URL=https://ark.cn-beijing.volces.com/api/v3/chat/completions
VITE_REASONING_API_KEY=your_reasoning_api_key_here
VITE_REASONING_MODEL=deepseek-r1-distill-qwen-32b-250120

# Embedding API Configuration (for document processing and RAG)
VITE_EMBEDDING_API_URL=https://ark.cn-beijing.volces.com/api/v3/embeddings
VITE_EMBEDDING_API_KEY=your_embedding_api_key_here
VITE_EMBEDDING_MODEL=doubao-embedding-text-240515

# =============================================================================
# ALTERNATIVE PROVIDERS (uncomment to use)
# =============================================================================

# OpenAI:
# VITE_EMBEDDING_API_URL=https://api.openai.com/v1/embeddings
# VITE_EMBEDDING_API_KEY=sk-your-openai-api-key-here
# VITE_EMBEDDING_MODEL=text-embedding-ada-002

# Azure OpenAI:
# VITE_EMBEDDING_API_URL=https://your-resource.openai.azure.com/openai/deployments/your-deployment/embeddings?api-version=2023-05-15
# VITE_EMBEDDING_API_KEY=your-azure-openai-key-here
# VITE_EMBEDDING_MODEL=text-embedding-ada-002

# =============================================================================
# SETUP INSTRUCTIONS
# =============================================================================
# 1. Copy this file to .env: cp .env.example .env
# 2. Replace placeholder values with real API keys
# 3. Test by uploading a document and checking browser console for errors