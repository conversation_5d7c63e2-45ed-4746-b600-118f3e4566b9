import { useEffect, useRef } from 'react'
import { ChatSession, Document } from '@/types'
import { processMessage } from '@/services/chatService'
import { MessageBubble } from './MessageBubble'
import { ChatInput } from './ChatInput'

interface ChatContainerProps {
  currentSession: ChatSession | null
  selectedDocumentIds: string[]
  documents: Document[]
  isProcessing: boolean
  setIsLoading: (loading: boolean) => void
  setIsProcessing: (processing: boolean) => void
  onSessionUpdate: () => void
  onDocumentSelectionChange: (documentIds: string[]) => void
  onDocumentUpdate: () => void
}

export default function ChatContainer({
  currentSession,
  selectedDocumentIds,
  documents,
  isProcessing,
  setIsLoading,
  setIsProcessing,
  onSessionUpdate,
  onDocumentSelectionChange,
  onDocumentUpdate
}: ChatContainerProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [currentSession?.messages])

  const handleSendMessage = async (message: string) => {
    if (!currentSession || isProcessing) return

    setIsProcessing(true)
    setIsLoading(true)

    try {
      await processMessage(currentSession.id, message, selectedDocumentIds)
      onSessionUpdate()
    } catch (error) {
      console.error('Error processing message:', error)
    } finally {
      setIsProcessing(false)
      setIsLoading(false)
    }
  }

  if (!currentSession) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-600 mb-2">
            Welcome to ChatDoc
          </h2>
          <p className="text-gray-500">
            Start a new chat or select an existing session to begin
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col bg-white">
      {/* Chat Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {currentSession.messages.map((message) => (
          <MessageBubble
            key={message.id}
            message={message}
            onCopy={() => {}}
            onRefresh={() => {}}
            onThumbsUp={() => {}}
            onThumbsDown={() => {}}
          />
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Chat Input */}
      <ChatInput
        onSendMessage={handleSendMessage}
        disabled={isProcessing}
        placeholder={
          selectedDocumentIds.length > 0
            ? `Ask about ${selectedDocumentIds.length} selected document${selectedDocumentIds.length > 1 ? 's' : ''}...`
            : 'Ask me anything...'
        }
        documents={documents}
        selectedDocumentIds={selectedDocumentIds}
        onDocumentSelectionChange={onDocumentSelectionChange}
        onDocumentsUpdate={onDocumentUpdate}
      />
    </div>
  )
}
