# API Documentation

This document describes the API integrations and service architecture for ChatDoc v3.

## Environment Configuration

### Required Environment Variables

```env
# LLM/Reasoning API Configuration
VITE_REASONING_API_URL=your_llm_api_endpoint
VITE_REASONING_API_KEY=your_llm_api_key
VITE_REASONING_MODEL=your_llm_model_name

# Embedding API Configuration
VITE_EMBEDDING_API_URL=your_embedding_api_endpoint
VITE_EMBEDDING_API_KEY=your_embedding_api_key
VITE_EMBEDDING_MODEL=your_embedding_model_name
```

### Default Configurations

**DeepSeek (LLM)**:
- URL: `https://api.deepseek.com/v1/chat/completions`
- Model: `deepseek-chat`

**Doubao (Embeddings)**:
- URL: `https://ark.cn-beijing.volces.com/api/v3/embeddings`
- Model: `doubao-embedding-v1`

## Service Architecture

### ChatService (`src/services/chatService.ts`)

Orchestrates the RAG (Retrieval-Augmented Generation) flow:

#### Key Functions:
- `sendMessage(message, documents, sessionId)`: Main chat function
- `generateTitle(message)`: Auto-generates session titles
- `getRelevantContext(query, documents)`: Retrieves similar document chunks

#### RAG Flow:
1. Generate embedding for user query
2. Search for similar document chunks
3. Assemble context from retrieved chunks
4. Send request to LLM with context
5. Parse response and extract reasoning

#### Configuration:
- Max context chunks: 5
- Similarity threshold: 0.3
- Max tokens per chunk: 800
- Message history: Last 6 messages

### EmbeddingService (`src/services/embeddingService.ts`)

Handles document processing and vector similarity search:

#### Key Functions:
- `processDocument(document)`: Chunks and embeds document content
- `searchSimilarChunks(query, documents, threshold)`: Vector similarity search
- `chunkDocument(document)`: Splits document into overlapping chunks

#### Chunking Parameters:
- Chunk size: 800 characters
- Chunk overlap: 200 characters
- Minimum chunk length: 10 characters

#### Embedding Configuration:
- Timeout: 15 seconds
- Retry on failure: Yes
- Storage: localStorage with document metadata

### WebExtractionService (`src/services/webExtractionService.ts`)

Extracts content from web URLs with retry mechanisms:

#### Key Functions:
- `extractWebContent(url, options)`: Main extraction function
- `retryExtraction(url, options, retries)`: Exponential backoff retry

#### Features:
- CORS bypass via proxy server
- Authentication support (basic auth)
- Timeout handling (configurable, default 30s)
- Content cleaning and markdown conversion
- Retry logic with exponential backoff

#### Configuration:
- Max retries: 3
- Initial timeout: 30 seconds
- Proxy endpoint: `http://localhost:9527/api/web-proxy`

### DocumentApiService (`src/services/documentApiService.ts`)

Future-ready document management service:

#### Current Implementation:
- Handles document CRUD in localStorage
- Type-safe document operations
- Prepared for server-side integration

#### Planned Features:
- Server-side document storage
- Document sharing and permissions
- Version control
- Bulk operations

## API Request/Response Formats

### LLM API (OpenAI Compatible)

**Request Format:**
```typescript
interface LLMRequest {
  model: string
  messages: Array<{
    role: 'system' | 'user' | 'assistant'
    content: string
  }>
  temperature?: number
  max_tokens?: number
  stream?: boolean
}
```

**Response Format:**
```typescript
interface LLMResponse {
  choices: Array<{
    message: {
      role: 'assistant'
      content: string
    }
    finish_reason: string
  }>
  usage: {
    prompt_tokens: number
    completion_tokens: number
    total_tokens: number
  }
}
```

### Embedding API

**Request Format:**
```typescript
interface EmbeddingRequest {
  model: string
  input: string | string[]
  encoding_format?: 'float' | 'base64'
}
```

**Response Format:**
```typescript
interface EmbeddingResponse {
  data: Array<{
    object: 'embedding'
    embedding: number[]
    index: number
  }>
  model: string
  usage: {
    prompt_tokens: number
    total_tokens: number
  }
}
```

### Web Extraction API

**Request Format:**
```typescript
interface WebExtractionRequest {
  url: string
  timeout?: number
  auth?: {
    username: string
    password: string
  }
}
```

**Response Format:**
```typescript
interface WebExtractionResponse {
  success: boolean
  title?: string
  content?: string
  excerpt?: string
  url: string
  error?: string
}
```

## Error Handling

### API Error Types

1. **Network Errors**: Connection failures, timeouts
2. **Authentication Errors**: Invalid API keys, unauthorized access
3. **Rate Limiting**: API quota exceeded
4. **Content Errors**: Invalid URLs, content extraction failures
5. **Processing Errors**: Embedding generation failures, chunking errors

### Error Response Format

```typescript
interface APIError {
  error: string
  details?: string
  code?: string
  timestamp?: Date
}
```

### Retry Strategies

1. **LLM Requests**: Single retry with exponential backoff
2. **Embedding Requests**: Up to 3 retries with 15s timeout
3. **Web Extraction**: Up to 3 retries with increasing timeouts
4. **Proxy Requests**: Automatic failover and timeout handling

## Rate Limiting & Performance

### Optimization Strategies

1. **Embedding Caching**: Store embeddings with documents
2. **Similarity Search**: Efficient cosine similarity with thresholds
3. **Context Assembly**: Limit context size and chunk count
4. **Request Batching**: Process multiple chunks efficiently
5. **Timeout Management**: Configurable timeouts per service

### Performance Metrics

- **Document Processing**: ~2-5 seconds per document
- **Query Response**: ~1-3 seconds average
- **Embedding Generation**: ~500ms per chunk
- **Web Extraction**: ~5-15 seconds depending on content

## Security Considerations

### API Key Management
- Environment variables only (never in code)
- Client-side configuration (development only)
- Proxy server for CORS bypass
- No server-side key storage

### Content Security
- URL validation before extraction
- Content sanitization and filtering
- Timeout limits to prevent DoS
- Error message sanitization

### Data Storage
- Local storage only (no server persistence)
- No sensitive data in localStorage
- Session data encryption (planned)
- Document access control (planned)

## Testing & Debugging

### Debug Utilities

The application includes debug utilities (`src/utils/debugUtils.ts`):

```javascript
// Available in browser console
window.debugChatDoc.documents()     // List all documents
window.debugChatDoc.sessions()      // List all sessions
window.debugChatDoc.embeddings()    // List embeddings
window.debugChatDoc.clearAll()      // Clear all data
```

### Testing Strategies

1. **Unit Tests**: Service layer functions and utilities
2. **Integration Tests**: API communication and data flow
3. **Component Tests**: UI interactions and state management
4. **End-to-End Tests**: Complete user workflows

### Common Issues & Solutions

1. **CORS Errors**: Ensure proxy server is running on port 9527
2. **API Key Issues**: Verify environment variables are set correctly
3. **Embedding Failures**: Check network connectivity and API quotas
4. **Slow Responses**: Reduce document selection or check API latency
5. **Memory Issues**: Clear localStorage or reduce document size