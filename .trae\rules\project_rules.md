# ChatDoc v3 - Project Development Rules & Standards

This document outlines the development standards, conventions, and norms used in the ChatDoc v3 project based on the existing codebase patterns.

## 1. Tech Stack & Dependencies

### Core Technologies
- **Frontend Framework**: React 18.2.0 with TypeScript
- **Build Tool**: Vite 5.0.0
- **Styling**: Tailwind CSS 3.3.5
- **State Management**: React hooks (useState, useEffect) + Custom hooks
- **Routing**: React Router DOM 6.20.1 (simplified single-route architecture)
- **Icons**: Lucide React 0.294.0
- **Notifications**: Sonner 2.0.7
- **Error Handling**: React Error Boundary 6.0.0

### Development Tools
- **Linting**: ESLint with TypeScript support
- **Testing**: Vitest 0.34.6 with UI support
- **Package Manager**: npm (with package-lock.json)
- **Proxy Server**: Custom Express proxy for API routing

### Key Dependencies
- **Document Processing**: @mozilla/readability, dom-to-semantic-markdown, jsdom
- **Markdown Rendering**: react-markdown with remark-gfm
- **Syntax Highlighting**: react-syntax-highlighter
- **Date Handling**: date-fns
- **Class Management**: clsx
- **Utilities**: cors, express, http-proxy-middleware

## 2. Project Structure & Organization

```
src/
├── components/          # Reusable UI components
├── hooks/              # Custom React hooks
├── pages/              # Page-level components (minimal usage)
├── services/           # Business logic and API services
├── types/              # TypeScript type definitions
├── utils/              # Utility functions and helpers
├── test/               # Test files
├── App.tsx             # Root application component
├── main.tsx            # Application entry point
├── index.css           # Global styles
└── vite-env.d.ts       # Vite type definitions
```

### Directory Conventions
- **Components**: PascalCase naming (e.g., `MainChatInterface.tsx`)
- **Services**: camelCase with "Service" suffix (e.g., `embeddingService.ts`)
- **Utils**: camelCase with descriptive names (e.g., `documentUtils.ts`)
- **Types**: Single `index.ts` file for centralized type definitions

## 3. TypeScript Configuration & Standards

### Compiler Options
- **Target**: ES2020
- **Module**: ESNext with bundler resolution
- **JSX**: react-jsx
- **Strict Mode**: Enabled with additional strict checks
- **Path Mapping**: `@/*` maps to `./src/*`

### Type Safety Rules
- Enable `strict: true`
- Enable `noUnusedLocals: true`
- Enable `noUnusedParameters: true`
- Enable `noFallthroughCasesInSwitch: true`
- Use explicit return types for complex functions
- Prefer interfaces over types for object definitions

## 4. Code Style & Linting Rules

### ESLint Configuration
- Extends: `eslint:recommended`, `@typescript-eslint/recommended`
- Parser: `@typescript-eslint/parser`
- Plugins: `react-refresh`

### Key Rules
- `@typescript-eslint/no-unused-vars`: error
- `@typescript-eslint/explicit-function-return-type`: off
- `@typescript-eslint/explicit-module-boundary-types`: off
- `@typescript-eslint/no-explicit-any`: warn
- `react-refresh/only-export-components`: warn

### Code Style Standards
- Use 2-space indentation
- Prefer single quotes for strings
- Use trailing commas in multiline objects/arrays
- Use semicolons consistently
- Prefer const over let when possible
- Use descriptive variable and function names

## 5. Component Development Patterns

### Component Structure
```typescript
// 1. Imports (external libraries first, then internal)
import { useState, useEffect } from 'react'
import { SomeIcon } from 'lucide-react'
import { toast } from 'sonner'
import { ComponentType } from '@/types'
import { utilityFunction } from '@/utils/helpers'

// 2. Interface definitions
interface ComponentProps {
  prop1: string
  prop2?: boolean
  onAction: (data: ComponentType) => void
}

// 3. Component implementation
export default function ComponentName({ prop1, prop2, onAction }: ComponentProps) {
  // State declarations
  const [state, setState] = useState<StateType>(initialValue)
  
  // Effect hooks
  useEffect(() => {
    // Effect logic
  }, [dependencies])
  
  // Event handlers
  const handleAction = () => {
    // Handler logic
  }
  
  // Render
  return (
    <div className="component-styles">
      {/* JSX content */}
    </div>
  )
}
```

### Component Conventions
- Use default exports for components
- Define interfaces above component implementation
- Group related state variables together
- Use descriptive handler names (e.g., `handleSubmit`, `handleToggle`)
- Implement proper error boundaries for critical components
- Use React.memo() for performance optimization when needed

## 6. Service Layer Conventions

### Service Structure
```typescript
// Service file example: embeddingService.ts

// 1. Type imports
import { Document, DocumentChunk } from '@/types'

// 2. Constants
const CHUNK_SIZE = 800
const CHUNK_OVERLAP = 200

// 3. Configuration functions
export function isServiceConfigured(): { configured: boolean; message: string } {
  // Configuration check logic
}

// 4. Core service functions
export async function processDocument(document: Document): Promise<void> {
  // Service implementation
}

// 5. Utility functions
function helperFunction(): ReturnType {
  // Helper implementation
}
```

### Service Conventions
- Use named exports for service functions
- Include configuration validation functions
- Implement proper error handling with try-catch blocks
- Use async/await for asynchronous operations
- Include detailed JSDoc comments for complex functions
- Provide fallback mechanisms for external API failures

## 7. State Management Patterns

### Local State Management
- Use `useState` for component-level state
- Use `useEffect` for side effects and lifecycle management
- Implement custom hooks for reusable stateful logic
- Use `useRef` for DOM references and mutable values

### State Organization
```typescript
// Group related state
const [sessions, setSessions] = useState<ChatSession[]>([])
const [currentSession, setCurrentSession] = useState<ChatSession | null>(null)
const [isLoading, setLoading] = useState(false)

// UI state separate from data state
const [sidebarExpanded, setSidebarExpanded] = useState(true)
const [isDarkMode, setIsDarkMode] = useState(false)
```

### State Update Patterns
- Use functional updates for state that depends on previous state
- Implement optimistic updates where appropriate
- Use proper dependency arrays in useEffect hooks
- Clean up subscriptions and timeouts in useEffect cleanup functions

## 8. Styling Guidelines (Tailwind)

### Tailwind Configuration
```javascript
// tailwind.config.js
export default {
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    extend: {
      colors: {
        primary: {
          100: '#dbeafe',
          600: '#2563eb',
        },
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
    },
  },
}
```

### Styling Conventions
- Use Tailwind utility classes exclusively
- Define custom colors in theme extension
- Use consistent spacing scale (4, 8, 12, 16, etc.)
- Implement responsive design with mobile-first approach
- Use semantic color names in theme configuration
- Avoid custom CSS classes; prefer Tailwind utilities

### Class Organization
```typescript
// Group classes logically
className="
  flex items-center justify-between  // Layout
  px-4 py-2 mx-2                    // Spacing
  bg-white border border-gray-200   // Background & borders
  rounded-lg shadow-sm              // Visual effects
  hover:bg-gray-50                  // Interactive states
  transition-colors duration-200    // Animations
"
```

## 9. File Naming Conventions

### File Extensions
- **Components**: `.tsx` for React components with JSX
- **Services**: `.ts` for TypeScript service files
- **Utils**: `.ts` for utility functions
- **Types**: `.ts` for type definitions
- **Tests**: `.test.ts` or `.test.tsx`

### Naming Patterns
- **Components**: PascalCase (e.g., `MainChatInterface.tsx`)
- **Services**: camelCase with suffix (e.g., `embeddingService.ts`)
- **Utils**: camelCase descriptive (e.g., `documentUtils.ts`)
- **Constants**: UPPER_SNAKE_CASE
- **Variables/Functions**: camelCase

## 10. Import/Export Standards

### Import Organization
```typescript
// 1. External library imports
import { useState, useEffect } from 'react'
import { toast } from 'sonner'
import { SomeIcon } from 'lucide-react'

// 2. Internal type imports
import { ChatMessage, Document } from '@/types'

// 3. Internal service/utility imports
import { processDocument } from '@/services/embeddingService'
import { cn } from '@/utils/cn'

// 4. Internal component imports
import ChatSidebar from './ChatSidebar'
import MessageBubble from './MessageBubble'
```

### Export Conventions
- Use default exports for main components
- Use named exports for services and utilities
- Export types and interfaces from centralized `@/types`
- Use barrel exports in index files when appropriate

## 11. Error Handling Patterns

### Component Error Handling
```typescript
// Use Error Boundaries for component-level errors
function ErrorFallback({ error }: { error: Error }) {
  return (
    <div className="error-container">
      <h2>Something went wrong</h2>
      <p>{error.message}</p>
      <button onClick={() => window.location.reload()}>
        Reload page
      </button>
    </div>
  )
}

// Wrap components with ErrorBoundary
<ErrorBoundary FallbackComponent={ErrorFallback}>
  <MainComponent />
</ErrorBoundary>
```

### Service Error Handling
```typescript
// Use try-catch with proper error logging
export async function serviceFunction(): Promise<Result> {
  try {
    const result = await apiCall()
    return result
  } catch (error) {
    console.error('Service error:', error)
    toast.error('Operation failed')
    throw new Error(`Service failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}
```

## 12. Testing Setup

### Test Configuration
- **Framework**: Vitest 0.34.6
- **UI Testing**: @vitest/ui for visual test runner
- **Test Location**: `src/test/` directory

### Testing Conventions
- Write unit tests for utility functions
- Write integration tests for services
- Use descriptive test names
- Group related tests with `describe` blocks
- Mock external dependencies appropriately

## 13. Build & Development Workflow

### NPM Scripts
```json
{
  "dev": "concurrently \"npm run dev:vite\" \"npm run dev:proxy\"",
  "dev:vite": "vite",
  "dev:proxy": "node proxy-server.js",
  "build": "tsc && vite build",
  "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
  "preview": "vite preview",
  "test": "vitest",
  "test:ui": "vitest --ui"
}
```

### Development Workflow
1. Run `npm run dev` for development with proxy server
2. Use `npm run lint` before committing
3. Run `npm run test` for testing
4. Use `npm run build` for production builds
5. Use `npm run preview` to test production builds locally

## 14. Environment Configuration

### Environment Variables
```bash
# API Configuration
VITE_REASONING_API_URL=https://api.example.com/chat
VITE_REASONING_API_KEY=your_api_key
VITE_EMBEDDING_API_URL=https://api.example.com/embeddings
VITE_EMBEDDING_API_KEY=your_embedding_key
VITE_EMBEDDING_MODEL=text-embedding-ada-002
```

### Configuration Patterns
- Use `VITE_` prefix for client-side environment variables
- Provide fallback values in code
- Validate configuration at runtime
- Use `.env.example` for documentation

## 15. Documentation Standards

### Code Documentation
```typescript
/**
 * Process document and generate embeddings for all chunks
 * @param document - The document to process
 * @throws {Error} When document processing fails
 */
export async function processDocument(document: Document): Promise<void> {
  // Implementation
}
```

### Documentation Conventions
- Use JSDoc comments for public functions
- Include parameter descriptions and return types
- Document error conditions with @throws
- Keep inline comments concise and meaningful
- Update documentation when changing functionality

### README Standards
- Include setup instructions
- Document environment variables
- Provide development workflow steps
- Include troubleshooting section
- Keep dependencies list updated

## 16. Performance Considerations

### Optimization Patterns
- Use React.memo() for expensive components
- Implement proper dependency arrays in useEffect
- Use abort controllers for API requests
- Implement timeout handling for external services
- Use lazy loading for large components when appropriate

### Bundle Optimization
- Configure Vite for optimal bundling
- Use dynamic imports for code splitting when needed
- Optimize asset loading and caching
- Monitor bundle size and performance metrics

---

*This document should be updated as the project evolves and new patterns emerge.*