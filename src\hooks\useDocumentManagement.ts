import { useState, useEffect } from 'react'
import { Document } from '@/types'
import { getDocuments } from '@/utils/documentUtils'

export function useDocumentManagement() {
  const [documents, setDocuments] = useState<Document[]>([])
  const [selectedDocumentIds, setSelectedDocumentIds] = useState<string[]>([])

  // Load documents on mount
  useEffect(() => {
    const loadedDocuments = getDocuments()
    setDocuments(loadedDocuments)
  }, [])

  const handleDocumentSelectionChange = (documentIds: string[]) => {
    setSelectedDocumentIds(documentIds)
  }

  const refreshDocuments = () => {
    const updatedDocuments = getDocuments()
    setDocuments(updatedDocuments)
    
    // Remove any selected document IDs that no longer exist
    setSelectedDocumentIds(prev => 
      prev.filter(id => updatedDocuments.some(doc => doc.id === id))
    )
  }

  return {
    documents,
    selectedDocumentIds,
    handleDocumentSelectionChange,
    refreshDocuments
  }
}
