import { useState, useEffect } from 'react'
import Chat<PERSON><PERSON>bar from './ChatSidebar'
import ChatContainer from './ChatContainer'
import { useSessionManagement } from '@/hooks/useSessionManagement'
import { useDocumentManagement } from '@/hooks/useDocumentManagement'
import { useChatState } from '@/hooks/useChatState'

export default function ChatLayout() {
  const [sidebarExpanded, setSidebarExpanded] = useState(true)

  // Custom hooks for state management
  const {
    sessions,
    currentSession,
    handleNewChat,
    handleSessionSelect,
    handleDeleteSession,
    handleToggleFavorite,
    handleRenameSession,
    refreshSessions
  } = useSessionManagement()

  const {
    documents,
    selectedDocumentIds,
    handleDocumentSelectionChange,
    refreshDocuments
  } = useDocumentManagement()

  const {
    setIsLoading,
    isProcessing,
    setIsProcessing
  } = useChatState()

  // Handle responsive behavior
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 768) {
        setSidebarExpanded(false)
      }
    }

    handleResize()
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.key === 'n') {
        e.preventDefault()
        handleNewChat()
      }
      if (e.ctrlKey && e.key === 'b') {
        e.preventDefault()
        setSidebarExpanded(prev => !prev)
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [handleNewChat])

  const handleToggleSidebar = () => {
    setSidebarExpanded(prev => !prev)
  }

  const handleSessionUpdate = () => {
    refreshSessions()
  }

  const handleDocumentUpdate = () => {
    refreshDocuments()
  }

  return (
    <div className="h-screen flex bg-gray-100">
      {/* Sidebar */}
      <ChatSidebar
        sessions={sessions}
        currentSession={currentSession}
        onSessionSelect={handleSessionSelect}
        onNewChat={handleNewChat}
        onDeleteSession={handleDeleteSession}
        onToggleFavorite={handleToggleFavorite}
        onRenameSession={handleRenameSession}
        documents={documents}
        selectedDocumentIds={selectedDocumentIds}
        onDocumentSelectionChange={handleDocumentSelectionChange}
        isExpanded={sidebarExpanded}
        onToggleExpanded={handleToggleSidebar}
      />

      {/* Main Chat Area */}
      <ChatContainer
        currentSession={currentSession}
        selectedDocumentIds={selectedDocumentIds}
        documents={documents}
        isProcessing={isProcessing}
        setIsLoading={setIsLoading}
        setIsProcessing={setIsProcessing}
        onSessionUpdate={handleSessionUpdate}
        onDocumentSelectionChange={handleDocumentSelectionChange}
        onDocumentUpdate={handleDocumentUpdate}
      />

      {/* Mobile overlay when sidebar is open */}
      {sidebarExpanded && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={() => setSidebarExpanded(false)}
        />
      )}
    </div>
  )
}
